A.)  CLOUD STORAGE
-----------------------------------------
+++++++++++++++++++++++++++++++++++++++++

1. write() 

puter.fs.delete()
Deletes a file or directory.

Syntax
puter.fs.delete(path)
puter.fs.delete(path, options)
Parameters
path (String) (required)
Path of the file or directory to delete. If path is not absolute, it will be resolved relative to the app's root directory.

options (Object) (optional)
The options for the delete operation. The following options are supported:

recursive (Boolean) - Whether to delete the directory recursively. Defaults to true.
descendantsOnly (Boolean) - Whether to delete only the descendants of the directory and not the directory itself. Defaults to false.
Return value
A Promise that will resolve when the file or directory is deleted.

Examples
Delete a file

<html>
<body>
    <script src="https://js.puter.com/v2/"></script>
    <script>
        (async () => {
            // (1) Create a random file
            let filename = puter.randName();
            await puter.fs.write(filename, 'Hello, world!');
            puter.print('File created successfully<br>');

            // (2) Delete the file
            await puter.fs.delete(filename);
            puter.print('File deleted successfully');
        })();
    </script>
</body>
</html>
Delete a directory

<html>
<body>
    <script src="https://js.puter.com/v2/"></script>
    <script>
        (async () => {
            // (1) Create a random directory
            let dirname = puter.randName();
            await puter.fs.mkdir(dirname);
            puter.print('Directory created successfully<br>');

            // (2) Delete the directory
            await puter.fs.delete(dirname);
            puter.print('Directory deleted successfully');
        })();
    </script>
</body>
</html>

__________________________________________
2. read()


puter.fs.read()
Reads data from a file.

Syntax
puter.fs.read(path)
Parameters
path (String) (required)
Path of the file to read. If path is not absolute, it will be resolved relative to the app's root directory.

Return value
A Promise that will resolve to a Blob object containing the contents of the file.

Examples
Read a file

<html>
<body>
    <script src="https://js.puter.com/v2/"></script>
    <script>
        (async () => {
            // (1) Create a random text file
            let filename = puter.randName() + ".txt";
            await puter.fs.write(filename, "Hello world! I'm a file!");
            puter.print(`"${filename}" created<br>`);

            // (2) Read the file and print its contents
            let blob = await puter.fs.read(filename);
            let content = await blob.text();
            puter.print(`"${filename}" read (content: "${content}")<br>`);
        })();
    </script>
</body>
</html>


_____________________________________
3. mkdir()



puter.fs.mkdir()
Allows you to create a directory.

Syntax
puter.fs.mkdir(path)
puter.fs.mkdir(path, options)
Parameters
path (string) (required)
The path to the directory to create. If path is not absolute, it will be resolved relative to the app's root directory.

options (object)
The options for the mkdir operation. The following options are supported:

overwrite (boolean) - Whether to overwrite the directory if it already exists. Defaults to false.
dedupeName (boolean) - Whether to deduplicate the directory name if it already exists. Defaults to false.
createMissingParents (boolean) - Whether to create missing parent directories. Defaults to false.
Return value
Returns a promise that resolves to the directory object of the created directory.

Examples
Create a new directory

<html>
<body>
    <script src="https://js.puter.com/v2/"></script>
    <script>
        // Create a directory with random name
        let dirName = puter.randName();
        puter.fs.mkdir(dirName).then((directory) => {
            puter.print(`"${dirName}" created at ${directory.path}`);
        }).catch((error) => {
            puter.print('Error creating directory:', error);
        });
    </script>
</body>
</html>
Demonstrate the use of dedupeName

<html>
<body>
    <script src="https://js.puter.com/v2/"></script>
    <script>
        (async () => {
            // create a directory named 'hello'
            let dir_1 = await puter.fs.mkdir('hello');
            puter.print(`Directory 1: ${dir_1.name}<br>`);
            // create a directory named 'hello' again, it should be automatically renamed to 'hello (n)' where n is the next available number
            let dir_2 = await puter.fs.mkdir('hello', { dedupeName: true });
            puter.print(`Directory 2: ${dir_2.name}<br>`);
        })();
    </script>
</body>
</html>
Demonstrate the use of createMissingParents

<html>
<body>
    <script src="https://js.puter.com/v2/"></script>
    <script>
        (async () => {
            // Create a directory named 'hello' in a directory that does not exist
            let dir = await puter.fs.mkdir('my-directory/another-directory/hello', { createMissingParents: true });
            puter.print(`Directory created at: ${dir.path}<br>`);
        })();
    </script>
</body>
</html>



____________________________________
4. readdir()


puter.fs.readdir()
Reads the contents of a directory, returning an array of items (files and directories) within it. This method is useful for listing all items in a specified directory in the Puter cloud storage.

Syntax
puter.fs.readdir(path)
Parameters
path (string) The path to the directory to read. If path is not absolute, it will be resolved relative to the app's root directory.

Return value
A Promise that resolves to an array of fsitems (files and directories) within the specified directory.

Examples
Read a directory

<html>
<body>
    <script src="https://js.puter.com/v2/"></script>
    <script>
        puter.fs.readdir('./').then((items) => {
            // print the path of each item in the directory
            puter.print(`Items in the directory:<br>${items.map((item) => item.path)}<br>`);
        }).catch((error) => {
            puter.print(`Error reading directory: ${error}`);
        });
    </script>
</body>
</html>


________________________________________
5. rename()


puter.fs.rename()
Renames a file or directory to a new name. This method allows you to change the name of a file or directory in the Puter cloud storage.

Syntax
puter.fs.rename(path, newName)
Parameters
path (string)
The path to the file or directory to rename. If path is not absolute, it will be resolved relative to the app's root directory.

newName (string)
The new name of the file or directory.

Return value
Returns a promise that resolves to the file or directory object of the renamed file or directory.

Examples
Rename a file

<html>
<body>
    <script src="https://js.puter.com/v2/"></script>
    <script>
        (async () => {
            // Create hello.txt
            await puter.fs.write('hello.txt', 'Hello, world!');
            puter.print(`"hello.txt" created<br>`);

            // Rename hello.txt to hello-world.txt
            await puter.fs.rename('hello.txt', 'hello-world.txt')
            puter.print(`"hello.txt" renamed to "hello-world.txt"<br>`);
        })();
    </script>
</body>
</html>



___________________________________
6. copy()



puter.fs.copy()
Copies a file or directory from one location to another.

Syntax
puter.fs.copy(source, destination)
puter.fs.copy(source, destination, options)
Parameters
source (String) (Required)
The path to the file or directory to copy.

destination (String) (Required)
The path to the destination directory. If destination is a directory then the file or directory will be copied into that directory using the same name as the source file or directory. If the destination is a file, we overwrite if overwrite is true, otherwise we error.

options (Object) (Optional)
The options for the copy operation. The following options are supported:

overwrite (Boolean) - Whether to overwrite the destination file or directory if it already exists. Defaults to false.
dedupeName (Boolean) - Whether to deduplicate the file or directory name if it already exists. Defaults to false.
newName (String) - The new name to use for the copied file or directory. Defaults to undefined.
Return value
A Promise that will resolve to the copied file or directory. If the source file or directory does not exist, the promise will be rejected with an error.

Examples
Copy a file

<html>
<body>
    <script src="https://js.puter.com/v2/"></script>
    <script>
    (async () => {
        // (1) Create a random text file
        let filename = puter.randName() + '.txt';
        await puter.fs.write(filename, 'Hello, world!');
        puter.print(`Created file: "${filename}"<br>`);

        // (2) create a random directory
        let dirname = puter.randName();
        await puter.fs.mkdir(dirname);
        puter.print(`Created directory: "${dirname}"<br>`);

        // (3) Copy the file into the directory
        puter.fs.copy(filename, dirname).then((file)=>{
            puter.print(`Copied file: "${filename}" to directory "${dirname}"<br>`);
        }).catch((error)=>{
            puter.print(`Error copying file: "${error}"<br>`);
        });
    })()
    </script>
</body>
</html>


_____________________________________
7. move()



puter.fs.move()
Moves a file or a directory from one location to another.

Syntax
puter.fs.move(source, destination)
puter.fs.move(source, destination, options)
Parameters
source (String) (Required)
The path to the file or directory to move.

destination (String) (Required)
The path to the destination directory. If destination is a directory then the file or directory will be moved into that directory using the same name as the source file or directory. If the destination is a file, we overwrite if overwrite is true, otherwise we error.

options (Object) (Optional)
The options for the move operation. The following options are supported:

overwrite (Boolean) - Whether to overwrite the destination file or directory if it already exists. Defaults to false.
dedupeName (Boolean) - Whether to deduplicate the file or directory name if it already exists. Defaults to false.
createMissingParents (Boolean) - Whether to create missing parent directories. Defaults to false.
Return value
A Promise that will resolve to the moved file or directory. If the source file or directory does not exist, the promise will be rejected with an error.

Examples
Move a file

<html>
<body>
    <script src="https://js.puter.com/v2/"></script>
    <script>
    (async () => {
        // (1) Create a random text file
        let filename = puter.randName() + '.txt';
        await puter.fs.write(filename, 'Hello, world!');
        puter.print(`Created file: ${filename}<br>`);

        // (2) create a random directory
        let dirname = puter.randName();
        await puter.fs.mkdir(dirname);
        puter.print(`Created directory: ${dirname}<br>`);

        // (3) Move the file into the directory
        await puter.fs.move(filename, dirname);
        puter.print(`Moved file: ${filename} to directory ${dirname}<br>`);

        // (4) Delete the file and directory (cleanup)
        await puter.fs.delete(dirname + '/' + filename);
        await puter.fs.delete(dirname);
    })();
    </script>
</body>
</html>
Demonstrate the createMissingParents option

<html>
<body>
    <script src="https://js.puter.com/v2/"></script>
    <script>
    (async () => {
        // (1) Create a random file
        let filename = puter.randName() + '.txt';
        await puter.fs.write(filename, 'Hello, world!');
        puter.print('Created file: ' + filename + '<br>');

        // (2) Move the file into a non-existent directory
        let dirname = puter.randName();
        await puter.fs.move(filename, dirname + '/' + filename, { createMissingParents: true });
        puter.print(`Moved ${filename} to ${dirname}<br>`);

        // (3) Delete the file and directory (cleanup)
        await puter.fs.delete('non-existent-directory/' + filename);
        await puter.fs.delete('non-existent-directory');
    })();
    </script>
</body>
</html>


______________________________
8. stat()


puter.fs.stat()
This method allows you to get information about a file or directory.

Syntax
puter.fs.stat(path)
Parameters
path (string) (required)
The path to the file or directory to get information about. If path is not absolute, it will be resolved relative to the app's root directory.

Return value
A Promise that resolves to the fsitem of the specified file or directory.

Examples
Get information about a file

<html>
<body>
    <script src="https://js.puter.com/v2/"></script>
    <script>
        (async () => {
            // () create a file
            await puter.fs.write('hello.txt', 'Hello, world!');
            puter.print('hello.txt created<br>');

            // (2) get information about hello.txt
            const file = await puter.fs.stat('hello.txt');
            puter.print(`hello.txt name: ${file.name}<br>`);
            puter.print(`hello.txt path: ${file.path}<br>`);
            puter.print(`hello.txt size: ${file.size}<br>`);
            puter.print(`hello.txt created: ${file.created}<br>`);
        })()
    </script>
</body>
</html>



_______________________________________
9. delete()


puter.fs.delete()
Deletes a file or directory.

Syntax
puter.fs.delete(path)
puter.fs.delete(path, options)
Parameters
path (String) (required)
Path of the file or directory to delete. If path is not absolute, it will be resolved relative to the app's root directory.

options (Object) (optional)
The options for the delete operation. The following options are supported:

recursive (Boolean) - Whether to delete the directory recursively. Defaults to true.
descendantsOnly (Boolean) - Whether to delete only the descendants of the directory and not the directory itself. Defaults to false.
Return value
A Promise that will resolve when the file or directory is deleted.

Examples
Delete a file

<html>
<body>
    <script src="https://js.puter.com/v2/"></script>
    <script>
        (async () => {
            // (1) Create a random file
            let filename = puter.randName();
            await puter.fs.write(filename, 'Hello, world!');
            puter.print('File created successfully<br>');

            // (2) Delete the file
            await puter.fs.delete(filename);
            puter.print('File deleted successfully');
        })();
    </script>
</body>
</html>
Delete a directory

<html>
<body>
    <script src="https://js.puter.com/v2/"></script>
    <script>
        (async () => {
            // (1) Create a random directory
            let dirname = puter.randName();
            await puter.fs.mkdir(dirname);
            puter.print('Directory created successfully<br>');

            // (2) Delete the directory
            await puter.fs.delete(dirname);
            puter.print('Directory deleted successfully');
        })();
    </script>
</body>
</html>


_______________________________
10. upload()


puter.fs.upload()
Given a number of local items, upload them to the Puter filesystem.

Syntax
puter.fs.upload(items)
puter.fs.upload(items, dirPath)
puter.fs.upload(items, dirPath, options)
Parameters
items (Array) (required)
The items to upload to the Puter filesystem. items can be an InputFileList, FileList, Array of File objects, or an Array of Blob objects.

dirPath (String) (optional)
The path of the directory to upload the items to. If not set, the items will be uploaded to the app's root directory.

options (Object) (optional)
A set of key/value pairs that configure the upload process.

Return value
Returns a promise that resolves to an array of file objects of the uploaded files.

Examples
Upload a file from a file input

<html>
<body>
    <script src="https://js.puter.com/v2/"></script>
    <input type="file" id="file-input" />
    <script>
        // File input
        let fileInput = document.getElementById('file-input');

        // Upload the file when the user selects it
        fileInput.onchange = () => {
            puter.fs.upload(fileInput.files).then((file) => {
                puter.print(`File uploaded successfully to: ${file.path}`);                
            })
        };
    </script>
</body>
</html>


-----------------------------------------
+++++++++++++++++++++++++++++++++++++++++

B.)  HOSTING 

+++++++++++++++++++++++++++++++++++++++++
-----------------------------------------


1. create()



puter.hosting.create()
Will create a new subdomain that will be served by the hosting service. Optionally, you can specify a path to a directory that will be served by the subdomain.

Syntax
puter.hosting.create(subdomain, dirPath)
Parameters
subdomain (String) (required)
A string containing the name of the subdomain you want to create.

dirPath (String) (optional)
A string containing the path to the directory you want to serve. If not specified, the subdomain will be created without a directory.

Return value
A Promise that will resolve to a subdomain object when the subdomain has been created. If a subdomain with the given name already exists, the promise will be rejected with an error. If the path does not exist, the promise will be rejected with an error.

Examples
Create a simple website displaying "Hello world!"

<html>
<body>
    <script src="https://js.puter.com/v2/"></script>
    <script>
        (async () => {
            // (1) Create a random directory
            let dirName = puter.randName();
            await puter.fs.mkdir(dirName)

            // (2) Create 'index.html' in the directory with the contents "Hello, world!"
            await puter.fs.write(`${dirName}/index.html`, '<h1>Hello, world!</h1>');

            // (3) Host the directory under a random subdomain
            let subdomain = puter.randName();
            const site = await puter.hosting.create(subdomain, dirName)

            puter.print(`Website hosted at: <a href="https://${site.subdomain}.puter.site" target="_blank">https://${site.subdomain}.puter.site</a>`);
        })();
    </script>
</body>
</html>


________________________________
2. list()


puter.hosting.list()
Returns an array of all subdomains in the user's subdomains that this app has access to. If the user has no subdomains, the array will be empty.

Syntax
puter.hosting.list()
Parameters
None

Return value
A Promise that will resolve to an array of all subdomains belonging to the user that this app has access to.

Examples
Create 3 random websites and then list them

<html>
<body>
    <script src="https://js.puter.com/v2/"></script>
    <script>
        (async () => {
            // (1) Generate 3 random subdomains
            let site_1 = puter.randName();
            let site_2 = puter.randName();
            let site_3 = puter.randName();

            // (2) Create 3 empty websites with the subdomains we generated
            await puter.hosting.create(site_1);
            await puter.hosting.create(site_2);
            await puter.hosting.create(site_3);

            // (3) Get all subdomains
            let sites = await puter.hosting.list();

            // (4) Display the names of the websites
            puter.print(sites.map(site => site.subdomain));

            // Delete all sites (cleanup)
            await puter.hosting.delete(site_1);
            await puter.hosting.delete(site_2);
            await puter.hosting.delete(site_3);
        })();
    </script>
</body>
</html>


__________________________________
3. delete()


puter.hosting.delete()
Deletes a subdomain from your account. The subdomain will no longer be served by the hosting service. If the subdomain has a directory, it will be disconnected from the subdomain. The associated directory will not be deleted.

Syntax
puter.hosting.delete(subdomain)
Parameters
subdomain (String) (required)
A string containing the name of the subdomain you want to delete.

Return value
A Promise that will resolve to true when the subdomain has been deleted. If a subdomain with the given name does not exist, the promise will be rejected with an error.

Examples
Create a random website then delete it

<html>
<body>
    <script src="https://js.puter.com/v2/"></script>
    <script>
        (async () => {
            // (1) Create a random website
            let subdomain = puter.randName();
            const site = await puter.hosting.create(subdomain)
            puter.print(`Website hosted at: ${site.subdomain}.puter.site (This is an empty website with no files)<br>`);

            // (2) Delete the website using delete()
            const site2 = await puter.hosting.delete(site.subdomain);
            puter.print('Website deleted<br>');

            // (3) Try to retrieve the website (should fail)
            puter.print('Trying to retrieve website... (should fail)<br>');
            try {
                await puter.hosting.get(site.subdomain);
            } catch (e) {
                puter.print('Website could not be retrieved<br>');
            }
        })();
    </script>
</body>
</html>


__________________________________
4. update()


puter.hosting.update()
Updates a subdomain to point to a new directory. If directory is not specified, the subdomain will be disconnected from its directory.

Syntax
puter.hosting.update(subdomain, dirPath)
Parameters
subdomain (String) (required)
A string containing the name of the subdomain you want to update.

dirPath (String) (optional)
A string containing the path to the directory you want to serve. If not specified, the subdomain will be disconnected from its directory.

Return value
A Promise that will resolve to a subdomain object when the subdomain has been updated. If a subdomain with the given name does not exist, the promise will be rejected with an error. If the path does not exist, the promise will be rejected with an error.

Examples
Update a subdomain to point to a new directory

<html>
<body>
    <script src="https://js.puter.com/v2/"></script>
    <script>
        (async () => {
            // (1) Create a random website
            let subdomain = puter.randName();
            const site = await puter.hosting.create(subdomain)
            puter.print(`Website hosted at: ${site.subdomain}.puter.site<br>`);

            // (2) Create a random directory
            let dirName = puter.randName();
            let dir = await puter.fs.mkdir(dirName)
            puter.print(`Created directory "${dir.path}"<br>`);

            // (3) Update the site with the new random directory
            await puter.hosting.update(subdomain, dirName)
            puter.print(`Changed subdomain's root directory to "${dir.path}"<br>`);

            // (4) Delete the app (cleanup)
            await puter.hosting.delete(updatedSite.subdomain)
        })();
    </script>
</body>
</html>


_________________________
5. get()


puter.hosting.get()
Returns a subdomain. If the subdomain does not exist, the promise will be rejected with an error.

Syntax
puter.hosting.get(subdomain)
Parameters
subdomain (String) (required)
A string containing the name of the subdomain you want to retrieve.

Return value
A Promise that will resolve to a subdomain object when the subdomain has been retrieved. If a subdomain with the given name does not exist, the promise will be rejected with an error.

Examples
Get a subdomain

<html>
<body>
    <script src="https://js.puter.com/v2/"></script>
    <script>
        (async () => {
            // (1) Create a random website
            let subdomain = puter.randName();
            const site = await puter.hosting.create(subdomain)
            puter.print(`Website hosted at: ${site.subdomain}.puter.site (This is an empty website with no files)<br>`);

            // (2) Retrieve the website using get()
            const site2 = await puter.hosting.get(site.subdomain);
            puter.print(`Website retrieved: subdomain=${site2.subdomain}.puter.site UID=${site2.uid}<br>`);

            // (3) Delete the website (cleanup)
            await puter.hosting.delete(subdomain);
        })();
    </script>
</body>
</html>