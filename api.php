<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Simple API endpoints for additional functionality
$request_method = $_SERVER['REQUEST_METHOD'];
$path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$path_parts = explode('/', trim($path, '/'));

// Remove 'api.php' from path if present
if (end($path_parts) === 'api.php') {
    array_pop($path_parts);
}

$endpoint = end($path_parts);

try {
    switch ($request_method) {
        case 'GET':
            handleGet($endpoint);
            break;
        case 'POST':
            handlePost($endpoint);
            break;
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}

function handleGet($endpoint) {
    switch ($endpoint) {
        case 'health':
            echo json_encode(['status' => 'OK', 'timestamp' => time()]);
            break;
        case 'info':
            echo json_encode([
                'app' => 'Puter Cloud Manager',
                'version' => '1.0.0',
                'description' => 'A web-based application for managing Puter.js hosting and cloud storage'
            ]);
            break;
        default:
            http_response_code(404);
            echo json_encode(['error' => 'Endpoint not found']);
            break;
    }
}

function handlePost($endpoint) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    switch ($endpoint) {
        case 'validate-subdomain':
            validateSubdomain($input);
            break;
        case 'generate-share-url':
            generateShareUrl($input);
            break;
        default:
            http_response_code(404);
            echo json_encode(['error' => 'Endpoint not found']);
            break;
    }
}

function validateSubdomain($input) {
    $subdomain = $input['subdomain'] ?? '';
    
    // Basic validation rules
    $errors = [];
    
    if (empty($subdomain)) {
        $errors[] = 'Subdomain is required';
    }
    
    if (strlen($subdomain) < 3) {
        $errors[] = 'Subdomain must be at least 3 characters long';
    }
    
    if (strlen($subdomain) > 63) {
        $errors[] = 'Subdomain must be less than 64 characters long';
    }
    
    if (!preg_match('/^[a-z0-9-]+$/', $subdomain)) {
        $errors[] = 'Subdomain can only contain lowercase letters, numbers, and hyphens';
    }
    
    if (preg_match('/^-|-$/', $subdomain)) {
        $errors[] = 'Subdomain cannot start or end with a hyphen';
    }
    
    // Reserved subdomains
    $reserved = ['www', 'api', 'admin', 'mail', 'ftp', 'localhost', 'test'];
    if (in_array($subdomain, $reserved)) {
        $errors[] = 'This subdomain is reserved';
    }
    
    echo json_encode([
        'valid' => empty($errors),
        'errors' => $errors
    ]);
}

function generateShareUrl($input) {
    $filename = $input['filename'] ?? '';
    
    if (empty($filename)) {
        http_response_code(400);
        echo json_encode(['error' => 'Filename is required']);
        return;
    }
    
    // Generate a simple share URL
    // In a real application, you might want to create a temporary token
    $shareId = md5($filename . time());
    $shareUrl = "https://" . $_SERVER['HTTP_HOST'] . "/share/" . $shareId;
    
    echo json_encode([
        'shareUrl' => $shareUrl,
        'shareId' => $shareId,
        'filename' => $filename,
        'expiresAt' => date('Y-m-d H:i:s', strtotime('+7 days'))
    ]);
}

// Utility function to log errors
function logError($message) {
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] $message" . PHP_EOL;
    file_put_contents('error.log', $logMessage, FILE_APPEND | LOCK_EX);
}

// Utility function to sanitize input
function sanitizeInput($input) {
    return htmlspecialchars(strip_tags(trim($input)));
}
?>
