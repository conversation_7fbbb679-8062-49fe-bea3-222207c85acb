<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Awesome Website</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        header {
            text-align: center;
            margin-bottom: 3rem;
            color: white;
        }
        
        h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .content {
            background: white;
            border-radius: 15px;
            padding: 3rem;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }
        
        .feature {
            text-align: center;
            padding: 2rem;
            border-radius: 10px;
            background: #f8f9fa;
            transition: transform 0.3s ease;
        }
        
        .feature:hover {
            transform: translateY(-5px);
        }
        
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        .cta {
            text-align: center;
            margin: 3rem 0;
        }
        
        .btn {
            display: inline-block;
            padding: 1rem 2rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            border-radius: 50px;
            font-weight: bold;
            transition: transform 0.3s ease;
        }
        
        .btn:hover {
            transform: scale(1.05);
        }
        
        footer {
            text-align: center;
            color: white;
            opacity: 0.8;
            margin-top: 2rem;
        }
        
        @media (max-width: 768px) {
            h1 {
                font-size: 2rem;
            }
            
            .content {
                padding: 2rem;
            }
            
            .features {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>🚀 My Awesome Website</h1>
            <p class="subtitle">Built with Puter Cloud Manager</p>
        </header>
        
        <div class="content">
            <h2>Welcome to My Website!</h2>
            <p>This is a demo website created using the Puter Cloud Manager. You can easily create beautiful, responsive websites like this one with just HTML, CSS, and JavaScript.</p>
            
            <div class="features">
                <div class="feature">
                    <div class="feature-icon">⚡</div>
                    <h3>Fast & Responsive</h3>
                    <p>Built with modern web technologies for optimal performance on all devices.</p>
                </div>
                
                <div class="feature">
                    <div class="feature-icon">🎨</div>
                    <h3>Beautiful Design</h3>
                    <p>Clean, modern interface with smooth animations and gradients.</p>
                </div>
                
                <div class="feature">
                    <div class="feature-icon">☁️</div>
                    <h3>Cloud Hosted</h3>
                    <p>Powered by Puter.js for reliable cloud hosting and storage.</p>
                </div>
            </div>
            
            <div class="cta">
                <a href="#" class="btn" onclick="showMessage()">Click Me!</a>
            </div>
        </div>
        
        <footer>
            <p>&copy; 2024 My Awesome Website. Created with ❤️ using Puter Cloud Manager.</p>
        </footer>
    </div>
    
    <script>
        function showMessage() {
            alert('Hello from your Puter-hosted website! 🎉\n\nYou can add any JavaScript functionality you want.');
        }
        
        // Add some interactive animations
        document.addEventListener('DOMContentLoaded', function() {
            const features = document.querySelectorAll('.feature');
            
            features.forEach((feature, index) => {
                feature.style.opacity = '0';
                feature.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    feature.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                    feature.style.opacity = '1';
                    feature.style.transform = 'translateY(0)';
                }, index * 200);
            });
        });
    </script>
</body>
</html>
