<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Puter Cloud Manager</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <h1 class="logo">
                    <i class="fas fa-cloud"></i>
                    Puter Cloud Manager
                </h1>
                <div class="header-controls">
                    <button id="theme-toggle" class="theme-toggle" title="Toggle Dark/Light Mode">
                        <i class="fas fa-moon"></i>
                    </button>
                </div>
            </div>
        </header>

        <!-- Navigation -->
        <nav class="nav">
            <button class="nav-btn active" data-section="hosting">
                <i class="fas fa-globe"></i>
                Hosting
            </button>
            <button class="nav-btn" data-section="storage">
                <i class="fas fa-cloud-upload-alt"></i>
                Cloud Storage
            </button>
        </nav>

        <!-- Main Content -->
        <main class="main">
            <!-- Hosting Section -->
            <section id="hosting" class="section active">
                <div class="section-header">
                    <h2><i class="fas fa-globe"></i> Website Hosting</h2>
                </div>

                <!-- Create Hosting Form -->
                <div class="card">
                    <h3><i class="fas fa-plus"></i> Create New Website</h3>
                    <form id="create-hosting-form" class="form">
                        <div class="form-group">
                            <label for="subdomain">Subdomain</label>
                            <input type="text" id="subdomain" name="subdomain" placeholder="my-awesome-site" required>
                            <small>Your site will be available at: <span id="subdomain-preview">your-subdomain</span>.puter.site</small>
                        </div>
                        
                        <div class="form-group">
                            <label for="website-content">Website Content (HTML/CSS/JS)</label>
                            <textarea id="website-content" name="content" rows="10" placeholder="Enter your HTML, CSS, and JavaScript code here..." required></textarea>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-rocket"></i>
                            Create Website
                        </button>
                    </form>
                </div>

                <!-- Hosted Websites List -->
                <div class="card">
                    <h3><i class="fas fa-list"></i> Manage Websites</h3>
                    <div id="hosted-websites" class="websites-grid">
                        <div class="loading" id="websites-loading">
                            <i class="fas fa-spinner fa-spin"></i>
                            Loading websites...
                        </div>
                    </div>
                </div>
            </section>

            <!-- Cloud Storage Section -->
            <section id="storage" class="section">
                <div class="section-header">
                    <h2><i class="fas fa-cloud-upload-alt"></i> Cloud Storage</h2>
                </div>

                <!-- Upload Form -->
                <div class="card">
                    <h3><i class="fas fa-upload"></i> Upload Files</h3>
                    <form id="upload-form" class="form">
                        <div class="upload-area" id="upload-area">
                            <i class="fas fa-cloud-upload-alt"></i>
                            <p>Drag & drop files here or click to browse</p>
                            <input type="file" id="file-input" multiple hidden>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-upload"></i>
                            Upload Files
                        </button>
                    </form>
                </div>

                <!-- Uploaded Files List -->
                <div class="card">
                    <h3><i class="fas fa-folder"></i> Your Files</h3>
                    <div id="uploaded-files" class="files-grid">
                        <div class="loading" id="files-loading">
                            <i class="fas fa-spinner fa-spin"></i>
                            Loading files...
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- Modals -->
        <!-- Edit Website Modal -->
        <div id="edit-modal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3><i class="fas fa-edit"></i> Edit Website</h3>
                    <button class="modal-close">&times;</button>
                </div>
                <form id="edit-form" class="form">
                    <div class="form-group">
                        <label for="edit-content">Website Content</label>
                        <textarea id="edit-content" rows="15" required></textarea>
                    </div>
                    <div class="modal-actions">
                        <button type="button" class="btn btn-secondary modal-close">Cancel</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            Save Changes
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Success/Error Messages -->
        <div id="toast-container" class="toast-container"></div>
    </div>

    <!-- Scripts -->
    <script src="https://js.puter.com/v2/"></script>
    <script src="script.js"></script>
</body>
</html>
