<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Puter.js Hosting & Storage</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://js.puter.com/v2/"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

        body {
            font-family: 'Inter', sans-serif;
            transition: background-color 0.3s, color 0.3s;
        }

        /* Light Mode */
        body.light-mode {
            background-color: #f0f4f8;
            color: #333;
        }
        body.light-mode .bg-gray-800 {
            background-color: #ffffff;
            color: #333;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        body.light-mode .text-gray-300 {
            color: #666;
        }
        body.light-mode .border-gray-700 {
            border-color: #e2e8f0;
        }
        body.light-mode input,
        body.light-mode textarea,
        body.light-mode select {
            background-color: #f8fafc;
            border-color: #cbd5e1;
            color: #333;
        }
        body.light-mode button {
            background-color: #3b82f6;
            color: white;
        }
        body.light-mode button:hover {
            background-color: #2563eb;
        }
        body.light-mode .bg-gray-700 {
            background-color: #edf2f7;
        }
        body.light-mode .text-gray-400 {
            color: #718096;
        }
        body.light-mode .hover\:bg-gray-600:hover {
            background-color: #e2e8f0;
        }
        body.light-mode .modal-content {
            background-color: #ffffff;
            color: #333;
            box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
        }
        body.light-mode .bg-blue-500 {
            background-color: #3b82f6;
        }
        body.light-mode .bg-red-500 {
            background-color: #ef4444;
        }
        body.light-mode .bg-green-500 {
            background-color: #22c55e;
        }
        body.light-mode .bg-yellow-500 {
            background-color: #eab308;
        }


        /* Dark Mode */
        body.dark-mode {
            background-color: #1a202c;
            color: #e2e8f0;
        }
        body.dark-mode .bg-gray-800 {
            background-color: #2d3748;
            color: #e2e8f0;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
        }
        body.dark-mode .text-gray-300 {
            color: #a0aec0;
        }
        body.dark-mode .border-gray-700 {
            border-color: #4a5568;
        }
        body.dark-mode input,
        body.dark-mode textarea,
        body.dark-mode select {
            background-color: #2d3748;
            border-color: #4a5568;
            color: #e2e8f0;
        }
        body.dark-mode button {
            background-color: #4299e1;
            color: white;
        }
        body.dark-mode button:hover {
            background-color: #3182ce;
        }
        body.dark-mode .bg-gray-700 {
            background-color: #4a5568;
        }
        body.dark-mode .text-gray-400 {
            color: #a0aec0;
        }
        body.dark-mode .hover\:bg-gray-600:hover {
            background-color: #2d3748;
        }
        body.dark-mode .modal-content {
            background-color: #2d3748;
            color: #e2e8f0;
            box-shadow: 0 10px 15px rgba(0, 0, 0, 0.5);
        }
        body.dark-mode .bg-blue-500 {
            background-color: #4299e1;
        }
        body.dark-mode .bg-red-500 {
            background-color: #f56565;
        }
        body.dark-mode .bg-green-500 {
            background-color: #48bb78;
        }
        body.dark-mode .bg-yellow-500 {
            background-color: #ecc94b;
        }

        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .animate-fadeIn {
            animation: fadeIn 0.5s ease-out forwards;
        }

        @keyframes slideInLeft {
            from { opacity: 0; transform: translateX(-20px); }
            to { opacity: 1; transform: translateX(0); }
        }
        .animate-slideInLeft {
            animation: slideInLeft 0.5s ease-out forwards;
        }

        .modal {
            display: none; /* Hidden by default */
            position: fixed; /* Stay in place */
            z-index: 1000; /* Sit on top */
            left: 0;
            top: 0;
            width: 100%; /* Full width */
            height: 100%; /* Full height */
            overflow: auto; /* Enable scroll if needed */
            background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background-color: #fefefe;
            margin: auto;
            padding: 20px;
            border-radius: 8px;
            width: 90%;
            max-width: 600px;
            position: relative;
            animation: fadeIn 0.3s;
        }

        .close-button {
            position: absolute;
            top: 10px;
            right: 15px;
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        .close-button:hover,
        .close-button:focus {
            color: #000;
            text-decoration: none;
            cursor: pointer;
        }
        body.dark-mode .close-button:hover,
        body.dark-mode .close-button:focus {
            color: #fff;
        }

        /* Custom scrollbar for textareas */
        textarea::-webkit-scrollbar {
            width: 8px;
        }

        textarea::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }

        textarea::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 10px;
        }

        textarea::-webkit-scrollbar-thumb:hover {
            background: #555;
        }

        body.dark-mode textarea::-webkit-scrollbar-track {
            background: #333;
        }

        body.dark-mode textarea::-webkit-scrollbar-thumb {
            background: #666;
        }

        body.dark-mode textarea::-webkit-scrollbar-thumb:hover {
            background: #999;
        }
    </style>
</head>
<body class="light-mode">
    <div class="min-h-screen flex flex-col items-center py-8 px-4 sm:px-6 lg:px-8">
        <header class="w-full max-w-4xl flex justify-between items-center mb-8">
            <h1 class="text-4xl font-bold text-blue-600">Puter.js App</h1>
            <button id="theme-toggle" class="p-2 rounded-full bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 shadow-md transition-all duration-300 hover:scale-105">
                <i class="fas fa-moon dark:hidden"></i>
                <i class="fas fa-sun hidden dark:inline-block"></i>
            </button>
        </header>

        <main class="w-full max-w-4xl bg-gray-800 rounded-lg shadow-xl p-6 space-y-10 border border-gray-700">

            <div id="message-box" class="hidden p-4 rounded-md text-sm font-medium" role="alert"></div>

            <section class="animate-fadeIn">
                <h2 class="text-2xl font-semibold text-gray-300 mb-4 flex items-center">
                    <i class="fas fa-globe mr-3 text-blue-400"></i> Create Hosting
                </h2>
                <div class="space-y-4">
                    <div>
                        <label for="create-subdomain" class="block text-sm font-medium text-gray-300">Subdomain Name:</label>
                        <input type="text" id="create-subdomain" class="mt-1 block w-full p-3 border border-gray-700 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 bg-gray-700 text-gray-200 placeholder-gray-400" placeholder="my-awesome-site">
                    </div>
                    <div>
                        <label for="create-content" class="block text-sm font-medium text-gray-300">Website Content (HTML, CSS, JS):</label>
                        <textarea id="create-content" rows="10" class="mt-1 block w-full p-3 border border-gray-700 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 bg-gray-700 text-gray-200 placeholder-gray-400" placeholder="<h1>Hello Puter!</h1><style>h1{color: blue;}</style><script>console.log('Loaded');</script>"></textarea>
                    </div>
                    <button id="create-hosting-btn" class="w-full py-3 px-4 bg-blue-500 hover:bg-blue-600 rounded-md text-white font-semibold shadow-md transition-all duration-300 hover:scale-105">
                        <i class="fas fa-plus-circle mr-2"></i> Create Hosting
                    </button>
                </div>
                <div id="create-hosting-info" class="mt-6 p-4 bg-gray-700 rounded-md hidden animate-fadeIn">
                    <h3 class="text-lg font-semibold text-gray-200 mb-2">Hosted Website Information:</h3>
                    <p class="text-gray-300"><strong class="text-blue-400">Subdomain ID:</strong> <span id="info-subdomain-id"></span></p>
                    <p class="text-300"><strong class="text-blue-400">Subdomain:</strong> <span id="info-subdomain"></span></p>
                    <p class="text-gray-300"><strong class="text-blue-400">Complete Link:</strong> <a id="info-complete-link" href="#" target="_blank" class="text-blue-400 hover:underline"></a></p>
                    <p class="text-gray-300"><strong class="text-blue-400">Directory:</strong> <span id="info-directory"></span></p>
                </div>
            </section>

            <section class="animate-fadeIn">
                <h2 class="text-2xl font-semibold text-gray-300 mb-4 flex items-center">
                    <i class="fas fa-tasks mr-3 text-green-400"></i> Manage Hosting
                    <button id="refresh-hostings-btn" class="ml-auto p-2 rounded-full bg-gray-700 text-gray-200 shadow-md transition-all duration-300 hover:scale-105">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </h2>
                <div id="hosted-websites-list" class="space-y-4">
                    <p class="text-gray-400 text-center">No hosted websites found. Create one above!</p>
                </div>
            </section>

            <section class="animate-fadeIn">
                <h2 class="text-2xl font-semibold text-gray-300 mb-4 flex items-center">
                    <i class="fas fa-cloud mr-3 text-purple-400"></i> Cloud Storage
                </h2>
                <div class="space-y-4">
                    <div>
                        <label for="upload-file" class="block text-sm font-medium text-gray-300">Upload File:</label>
                        <input type="file" id="upload-file" class="mt-1 block w-full text-gray-200 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-blue-500 file:text-white hover:file:bg-blue-600 transition-all duration-300">
                    </div>
                    <div id="uploaded-files-list" class="space-y-4">
                        <p class="text-gray-400 text-center">No files uploaded yet.</p>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <div id="edit-hosting-modal" class="modal">
        <div class="modal-content">
            <span class="close-button" id="close-edit-modal">&times;</span>
            <h2 class="text-2xl font-semibold mb-4">Edit Hosting</h2>
            <div class="space-y-4">
                <div>
                    <label for="edit-subdomain-name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Subdomain:</label>
                    <input type="text" id="edit-subdomain-name" class="mt-1 block w-full p-3 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200" readonly>
                </div>
                <div>
                    <label for="edit-content" class="block text-sm font-medium text-gray-700 dark:text-gray-300">New Website Content (HTML, CSS, JS):</label>
                    <textarea id="edit-content" rows="15" class="mt-1 block w-full p-3 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200" placeholder="<h1>Updated Content!</h1>"></textarea>
                </div>
                <button id="save-edit-btn" class="w-full py-3 px-4 bg-green-500 hover:bg-green-600 rounded-md text-white font-semibold shadow-md transition-all duration-300 hover:scale-105">
                    <i class="fas fa-save mr-2"></i> Save Changes
                </button>
            </div>
        </div>
    </div>

    <div id="confirmation-modal" class="modal">
        <div class="modal-content">
            <h2 class="text-xl font-semibold mb-4" id="confirmation-title">Confirm Action</h2>
            <p class="mb-6" id="confirmation-message">Are you sure you want to perform this action?</p>
            <div class="flex justify-end space-x-4">
                <button id="confirm-cancel-btn" class="py-2 px-4 bg-gray-300 hover:bg-gray-400 rounded-md text-gray-800 font-semibold transition-all duration-300">Cancel</button>
                <button id="confirm-action-btn" class="py-2 px-4 bg-red-500 hover:bg-red-600 rounded-md text-white font-semibold transition-all duration-300">Confirm</button>
            </div>
        </div>
    </div>

    <script>
        // Theme Toggle
        const themeToggleBtn = document.getElementById('theme-toggle');
        const body = document.body;

        // Set initial theme based on local storage or system preference
        const currentTheme = localStorage.getItem('theme');
        if (currentTheme) {
            body.classList.add(currentTheme);
        } else if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
            body.classList.add('dark-mode');
        } else {
            body.classList.add('light-mode');
        }

        themeToggleBtn.addEventListener('click', () => {
            if (body.classList.contains('light-mode')) {
                body.classList.remove('light-mode');
                body.classList.add('dark-mode');
                localStorage.setItem('theme', 'dark-mode');
            } else {
                body.classList.remove('dark-mode');
                body.classList.add('light-mode');
                localStorage.setItem('theme', 'light-mode');
            }
        });

        // Message Box Functionality
        const messageBox = document.getElementById('message-box');
        function showMessage(message, type = 'info') {
            messageBox.textContent = message;
            messageBox.classList.remove('hidden', 'bg-red-100', 'text-red-800', 'bg-green-100', 'text-green-800', 'bg-blue-100', 'text-blue-800');
            if (type === 'error') {
                messageBox.classList.add('bg-red-100', 'text-red-800');
            } else if (type === 'success') {
                messageBox.classList.add('bg-green-100', 'text-green-800');
            } else {
                messageBox.classList.add('bg-blue-100', 'text-blue-800');
            }
            messageBox.classList.remove('hidden');
            setTimeout(() => {
                messageBox.classList.add('hidden');
            }, 5000); // Hide after 5 seconds
        }

        // Confirmation Modal Functionality
        const confirmationModal = document.getElementById('confirmation-modal');
        const confirmationTitle = document.getElementById('confirmation-title');
        const confirmationMessage = document.getElementById('confirmation-message');
        const confirmCancelBtn = document.getElementById('confirm-cancel-btn');
        const confirmActionBtn = document.getElementById('confirm-action-btn');
        let confirmCallback = null;

        function showConfirmation(title, message, callback) {
            confirmationTitle.textContent = title;
            confirmationMessage.textContent = message;
            confirmCallback = callback;
            confirmationModal.style.display = 'flex';
        }

        confirmCancelBtn.addEventListener('click', () => {
            confirmationModal.style.display = 'none';
            if (confirmCallback) {
                confirmCallback(false);
            }
        });

        confirmActionBtn.addEventListener('click', () => {
            confirmationModal.style.display = 'none';
            if (confirmCallback) {
                confirmCallback(true);
            }
        });

        // --- Create Hosting ---
        const createSubdomainInput = document.getElementById('create-subdomain');
        const createContentTextarea = document.getElementById('create-content');
        const createHostingBtn = document.getElementById('create-hosting-btn');
        const createHostingInfoDiv = document.getElementById('create-hosting-info');
        const infoSubdomainId = document.getElementById('info-subdomain-id');
        const infoSubdomain = document.getElementById('info-subdomain');
        const infoCompleteLink = document.getElementById('info-complete-link');
        const infoDirectory = document.getElementById('info-directory');

        createHostingBtn.addEventListener('click', async () => {
            const subdomain = createSubdomainInput.value.trim();
            const content = createContentTextarea.value.trim();

            if (!subdomain) {
                showMessage('Please enter a subdomain name.', 'error');
                return;
            }
            if (!content) {
                showMessage('Please enter website content.', 'error');
                return;
            }

            try {
                // 1. Create a directory for the hosting content
                const dirName = `hosting-${subdomain}-${puter.randName()}`;
                const dir = await puter.fs.mkdir(dirName, { createMissingParents: true });
                showMessage(`Directory "${dir.path}" created.`, 'info');

                // 2. Write the content to index.html inside the directory
                await puter.fs.write(`${dir.path}/index.html`, content);
                showMessage(`Content written to ${dir.path}/index.html.`, 'info');

                // 3. Create the hosting
                const site = await puter.hosting.create(subdomain, dir.path);
                showMessage(`Hosting created successfully for ${site.subdomain}.puter.site!`, 'success');

                // Display information
                infoSubdomainId.textContent = site.uid;
                infoSubdomain.textContent = site.subdomain;
                infoCompleteLink.href = `https://${site.subdomain}.puter.site`;
                infoCompleteLink.textContent = `https://${site.subdomain}.puter.site`;
                infoDirectory.textContent = site.path || 'No specific directory linked (default app root)';
                createHostingInfoDiv.classList.remove('hidden');

                // Clear form fields
                createSubdomainInput.value = '';
                createContentTextarea.value = '';

                // Refresh hosted websites list
                await listHostedWebsites();

            } catch (error) {
                console.error('Error creating hosting:', error);
                showMessage(`Failed to create hosting: ${error.message || error}`, 'error');
            }
        });

        // --- Manage Hosting ---
        const hostedWebsitesList = document.getElementById('hosted-websites-list');
        const refreshHostingsBtn = document.getElementById('refresh-hostings-btn');

        async function listHostedWebsites() {
            hostedWebsitesList.innerHTML = '<p class="text-gray-400 text-center">Loading hosted websites...</p>';
            try {
                const sites = await puter.hosting.list();
                if (sites.length === 0) {
                    hostedWebsitesList.innerHTML = '<p class="text-gray-400 text-center">No hosted websites found. Create one above!</p>';
                    return;
                }

                hostedWebsitesList.innerHTML = ''; // Clear previous list
                sites.forEach(site => {
                    const siteElement = document.createElement('div');
                    siteElement.className = 'bg-gray-700 dark:bg-gray-700 p-4 rounded-md shadow-sm flex flex-col sm:flex-row justify-between items-start sm:items-center animate-slideInLeft';
                    siteElement.innerHTML = `
                        <div class="mb-3 sm:mb-0">
                            <p class="text-lg font-medium text-gray-200">${site.subdomain}.puter.site</p>
                            <p class="text-sm text-gray-400">Directory: ${site.path || 'Not linked'}</p>
                        </div>
                        <div class="flex flex-wrap gap-2">
                            <button class="visit-btn py-2 px-4 bg-blue-500 hover:bg-blue-600 rounded-md text-white font-semibold text-sm transition-all duration-300" data-subdomain="${site.subdomain}">
                                <i class="fas fa-external-link-alt mr-1"></i> Visit
                            </button>
                            <button class="edit-btn py-2 px-4 bg-yellow-500 hover:bg-yellow-600 rounded-md text-white font-semibold text-sm transition-all duration-300" data-subdomain="${site.subdomain}" data-dir-path="${site.path || ''}">
                                <i class="fas fa-edit mr-1"></i> Edit
                            </button>
                            <button class="delete-hosting-btn py-2 px-4 bg-red-500 hover:bg-red-600 rounded-md text-white font-semibold text-sm transition-all duration-300" data-subdomain="${site.subdomain}">
                                <i class="fas fa-trash-alt mr-1"></i> Delete
                            </button>
                        </div>
                    `;
                    hostedWebsitesList.appendChild(siteElement);
                });

                // Add event listeners for new buttons
                document.querySelectorAll('.visit-btn').forEach(button => {
                    button.addEventListener('click', (e) => {
                        const subdomain = e.currentTarget.dataset.subdomain;
                        window.open(`https://${subdomain}.puter.site`, '_blank');
                    });
                });

                document.querySelectorAll('.delete-hosting-btn').forEach(button => {
                    button.addEventListener('click', (e) => {
                        const subdomainToDelete = e.currentTarget.dataset.subdomain;
                        showConfirmation(
                            'Delete Hosting',
                            `Are you sure you want to permanently delete the hosting for "${subdomainToDelete}.puter.site"? This action cannot be undone.`,
                            async (confirmed) => {
                                if (confirmed) {
                                    await deleteHosting(subdomainToDelete);
                                }
                            }
                        );
                    });
                });

                document.querySelectorAll('.edit-btn').forEach(button => {
                    button.addEventListener('click', (e) => {
                        const subdomainToEdit = e.currentTarget.dataset.subdomain;
                        const dirPathToEdit = e.currentTarget.dataset.dirPath;
                        openEditHostingModal(subdomainToEdit, dirPathToEdit);
                    });
                });

            } catch (error) {
                console.error('Error listing hostings:', error);
                showMessage(`Failed to list hostings: ${error.message || error}`, 'error');
                hostedWebsitesList.innerHTML = '<p class="text-red-400 text-center">Error loading hosted websites.</p>';
            }
        }

        async function deleteHosting(subdomain) {
            try {
                await puter.hosting.delete(subdomain);
                showMessage(`Hosting "${subdomain}.puter.site" deleted successfully.`, 'success');
                await listHostedWebsites(); // Refresh the list
            } catch (error) {
                console.error('Error deleting hosting:', error);
                showMessage(`Failed to delete hosting "${subdomain}.puter.site": ${error.message || error}`, 'error');
            }
        }

        refreshHostingsBtn.addEventListener('click', listHostedWebsites);

        // --- Edit Hosting Modal ---
        const editHostingModal = document.getElementById('edit-hosting-modal');
        const closeEditModalBtn = document.getElementById('close-edit-modal');
        const editSubdomainNameInput = document.getElementById('edit-subdomain-name');
        const editContentTextarea = document.getElementById('edit-content');
        const saveEditBtn = document.getElementById('save-edit-btn');
        let currentEditingSubdomain = null;
        let currentEditingDirPath = null;

        closeEditModalBtn.addEventListener('click', () => {
            editHostingModal.style.display = 'none';
        });

        window.addEventListener('click', (event) => {
            if (event.target === editHostingModal) {
                editHostingModal.style.display = 'none';
            }
        });

        async function openEditHostingModal(subdomain, dirPath) {
            currentEditingSubdomain = subdomain;
            currentEditingDirPath = dirPath;
            editSubdomainNameInput.value = subdomain;
            editContentTextarea.value = 'Loading content...';
            editHostingModal.style.display = 'flex';

            try {
                if (dirPath) {
                    const blob = await puter.fs.read(`${dirPath}/index.html`);
                    const content = await blob.text();
                    editContentTextarea.value = content;
                } else {
                    editContentTextarea.value = 'No associated directory. Enter new content to create one.';
                }
            } catch (error) {
                console.error('Error reading file for editing:', error);
                editContentTextarea.value = 'Could not load existing content. Please enter new content.';
                showMessage(`Failed to load content for editing: ${error.message || error}`, 'error');
            }
        }

        saveEditBtn.addEventListener('click', async () => {
            const newContent = editContentTextarea.value.trim();
            if (!newContent) {
                showMessage('Please enter content to update the website.', 'error');
                return;
            }

            try {
                let targetDirPath = currentEditingDirPath;
                // If no directory path exists, create one
                if (!targetDirPath) {
                    targetDirPath = `hosting-${currentEditingSubdomain}-${puter.randName()}`;
                    await puter.fs.mkdir(targetDirPath, { createMissingParents: true });
                    showMessage(`New directory "${targetDirPath}" created for ${currentEditingSubdomain}.`, 'info');
                }

                // Write the new content to index.html
                await puter.fs.write(`${targetDirPath}/index.html`, newContent);
                showMessage(`Content updated in ${targetDirPath}/index.html.`, 'info');

                // Update the hosting to point to the new (or existing) directory
                await puter.hosting.update(currentEditingSubdomain, targetDirPath);
                showMessage(`Hosting "${currentEditingSubdomain}.puter.site" updated successfully.`, 'success');

                editHostingModal.style.display = 'none';
                await listHostedWebsites(); // Refresh the list
            } catch (error) {
                console.error('Error updating hosting:', error);
                showMessage(`Failed to update hosting "${currentEditingSubdomain}.puter.site": ${error.message || error}`, 'error');
            }
        });


        // --- Cloud Storage ---
        const uploadFileInput = document.getElementById('upload-file');
        const uploadedFilesList = document.getElementById('uploaded-files-list');

        uploadFileInput.addEventListener('change', async (event) => {
            const files = event.target.files;
            if (files.length === 0) {
                return;
            }

            try {
                // puter.fs.upload expects an array of File or Blob objects
                const uploaded = await puter.fs.upload(Array.from(files));
                showMessage(`File "${uploaded[0].name}" uploaded successfully!`, 'success');
                uploadFileInput.value = ''; // Clear the input
                await listUploadedFiles(); // Refresh the list
            } catch (error) {
                console.error('Error uploading file:', error);
                showMessage(`Failed to upload file: ${error.message || error}`, 'error');
            }
        });

        async function listUploadedFiles() {
            uploadedFilesList.innerHTML = '<p class="text-gray-400 text-center">Loading uploaded files...</p>';
            try {
                const items = await puter.fs.readdir('./'); // Read root directory
                const files = items.filter(item => item.type === 'file'); // Filter for files only

                if (files.length === 0) {
                    uploadedFilesList.innerHTML = '<p class="text-gray-400 text-center">No files uploaded yet.</p>';
                    return;
                }

                uploadedFilesList.innerHTML = ''; // Clear previous list
                files.forEach(file => {
                    const fileElement = document.createElement('div');
                    fileElement.className = 'bg-gray-700 dark:bg-gray-700 p-4 rounded-md shadow-sm flex flex-col sm:flex-row justify-between items-start sm:items-center animate-slideInLeft';
                    fileElement.innerHTML = `
                        <div class="mb-3 sm:mb-0">
                            <p class="text-lg font-medium text-gray-200">${file.name}</p>
                            <p class="text-sm text-gray-400">Size: ${(file.size / 1024).toFixed(2)} KB</p>
                        </div>
                        <div class="flex flex-wrap gap-2">
                            <button class="share-file-btn py-2 px-4 bg-green-500 hover:bg-green-600 rounded-md text-white font-semibold text-sm transition-all duration-300" data-file-path="${file.path}">
                                <i class="fas fa-share-alt mr-1"></i> Share
                            </button>
                            <button class="delete-file-btn py-2 px-4 bg-red-500 hover:bg-red-600 rounded-md text-white font-semibold text-sm transition-all duration-300" data-file-path="${file.path}">
                                <i class="fas fa-trash-alt mr-1"></i> Delete
                            </button>
                        </div>
                    `;
                    uploadedFilesList.appendChild(fileElement);
                });

                // Add event listeners for new buttons
                document.querySelectorAll('.share-file-btn').forEach(button => {
                    button.addEventListener('click', async (e) => {
                        const filePath = e.currentTarget.dataset.filePath;
                        try {
                            const publicUrl = await puter.fs.getPublicUrl(filePath);
                            // Copy to clipboard
                            const textarea = document.createElement('textarea');
                            textarea.value = publicUrl;
                            document.body.appendChild(textarea);
                            textarea.select();
                            document.execCommand('copy');
                            document.body.removeChild(textarea);
                            showMessage(`Public URL copied to clipboard: ${publicUrl}`, 'success');
                        } catch (error) {
                            console.error('Error generating public URL:', error);
                            showMessage(`Failed to generate public URL: ${error.message || error}`, 'error');
                        }
                    });
                });

                document.querySelectorAll('.delete-file-btn').forEach(button => {
                    button.addEventListener('click', (e) => {
                        const filePathToDelete = e.currentTarget.dataset.filePath;
                        showConfirmation(
                            'Delete File',
                            `Are you sure you want to permanently delete "${filePathToDelete}" from cloud storage? This action cannot be undone.`,
                            async (confirmed) => {
                                if (confirmed) {
                                    await deleteFile(filePathToDelete);
                                }
                            }
                        );
                    });
                });

            } catch (error) {
                console.error('Error listing files:', error);
                showMessage(`Failed to list files: ${error.message || error}`, 'error');
                uploadedFilesList.innerHTML = '<p class="text-red-400 text-center">Error loading uploaded files.</p>';
            }
        }

        async function deleteFile(filePath) {
            try {
                await puter.fs.delete(filePath);
                showMessage(`File "${filePath}" deleted successfully.`, 'success');
                await listUploadedFiles(); // Refresh the list
            } catch (error) {
                console.error('Error deleting file:', error);
                showMessage(`Failed to delete file "${filePath}": ${error.message || error}`, 'error');
            }
        }

        // Initial load of data
        window.onload = async () => {
            // puter.js does not seem to require an explicit init() call based on documentation.
            // The puter object should be available globally once the script is loaded.
            // Removed puter.init() as it was causing "puter.init is not a function" error.
            await listHostedWebsites();
            await listUploadedFiles();
        };

    </script>
</body>
</html>
