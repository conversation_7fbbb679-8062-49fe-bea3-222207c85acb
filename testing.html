<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Puter.js Testing</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 1.5rem;
            margin: 1rem 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 4px;
            cursor: pointer;
            margin: 0.5rem;
            font-size: 1rem;
        }
        button:hover {
            background: #0056b3;
        }
        .output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 1rem;
            margin: 1rem 0;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .error {
            color: #dc3545;
        }
        .success {
            color: #28a745;
        }
        .warning {
            color: #ffc107;
        }
    </style>
</head>
<body>
    <h1>Puter.js Delete Testing</h1>

    <div class="test-section">
        <h2>Website Inspection & Delete Testing</h2>
        <button onclick="inspectWebsite()">Inspect test-4xv4g39ud</button>
        <button onclick="testDelete()">Test Delete (Subdomain)</button>
        <button onclick="testDeleteWithUID()">Test Delete (UID)</button>
        <button onclick="listAllWebsites()">List All Websites</button>
        <button onclick="clearOutput()">Clear Output</button>
        <div id="output" class="output">Click a button to start testing...</div>
    </div>

    <script src="https://js.puter.com/v2/"></script>
    <script>
        function log(message, type = 'info') {
            const output = document.getElementById('output');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : '';
            output.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            output.scrollTop = output.scrollHeight;
        }

        function clearOutput() {
            document.getElementById('output').innerHTML = '';
        }

        async function inspectWebsite() {
            const subdomain = 'test-4xv4g39ud';
            log(`=== INSPECTING WEBSITE: ${subdomain} ===`);

            try {
                log('1. Checking with puter.hosting.get()...');
                try {
                    const getResult = await puter.hosting.get(subdomain);
                    log('✅ Found with get():', 'success');
                    log(JSON.stringify(getResult, null, 2));
                } catch (getError) {
                    log('❌ Not found with get():', 'error');
                    log(JSON.stringify(getError, null, 2));
                }

                log('2. Checking with puter.hosting.list()...');
                const allSites = await puter.hosting.list();
                log(`Found ${allSites.length} total websites`);

                const foundSite = allSites.find(site => site.subdomain === subdomain);
                if (foundSite) {
                    log('✅ Found in list:', 'success');
                    log(JSON.stringify(foundSite, null, 2));
                } else {
                    log('❌ Not found in list', 'error');
                    log('All websites:');
                    allSites.forEach(site => {
                        log(`  - ${site.subdomain}`);
                    });
                }

                log('3. Website URL check:');
                const websiteUrl = `https://${subdomain}.puter.site`;
                log(`URL: ${websiteUrl}`);
                log('Try visiting this URL manually to verify if it\'s live');

            } catch (error) {
                log('Error during inspection:', 'error');
                log(JSON.stringify(error, null, 2));
            }
        }

        async function testDelete() {
            const subdomain = 'test-4xv4g39ud';
            log(`=== TESTING DELETE: ${subdomain} ===`);

            try {
                log('Attempting to delete...');
                const result = await puter.hosting.delete(subdomain);
                log('✅ Delete successful!', 'success');
                log(`Result: ${result} (type: ${typeof result})`);

                // Verify deletion
                log('Verifying deletion...');
                try {
                    await puter.hosting.get(subdomain);
                    log('⚠️ WARNING: Website still exists after delete!', 'warning');
                } catch (verifyError) {
                    log('✅ Deletion verified - website no longer exists', 'success');
                }

            } catch (error) {
                log('❌ Delete failed:', 'error');
                log(`Error type: ${typeof error}`);
                log(`Error constructor: ${error?.constructor?.name}`);

                if (error && typeof error === 'object' && error.success === false && error.error) {
                    const apiError = error.error;
                    log(`API Error Code: ${apiError.code}`);
                    log(`API Error Message: ${apiError.message}`);
                    log(`API Error Status: ${apiError.status}`);

                    if (apiError.code === 'entity_not_found') {
                        log('This is an "entity not found" error - the website doesn\'t exist in Puter\'s system', 'warning');
                    }
                }

                log('Full error object:');
                log(JSON.stringify(error, null, 2));
            }
        }

        async function testDeleteWithUID() {
            log(`=== TESTING DELETE WITH UID ===`);

            try {
                // First get the website to find its UID
                log('Getting website details to find UID...');
                const allSites = await puter.hosting.list();
                const targetSite = allSites.find(site => site.subdomain === 'test-4xv4g39ud');

                if (!targetSite) {
                    log('❌ Website not found in list', 'error');
                    return;
                }

                const uid = targetSite.uid;
                log(`Found UID: ${uid}`);

                // Try deleting with UID instead of subdomain
                log('Attempting to delete with UID...');
                try {
                    const result = await puter.hosting.delete(uid);
                    log('✅ Delete with UID successful!', 'success');
                    log(`Result: ${result} (type: ${typeof result})`);
                } catch (uidError) {
                    log('❌ Delete with UID also failed:', 'error');
                    log(JSON.stringify(uidError, null, 2));
                }

            } catch (error) {
                log('Error in UID delete test:', 'error');
                log(JSON.stringify(error, null, 2));
            }
        }

        async function listAllWebsites() {
            log('=== LISTING ALL WEBSITES ===');

            try {
                const websites = await puter.hosting.list();
                log(`Found ${websites.length} websites:`, 'success');

                websites.forEach((site, index) => {
                    log(`${index + 1}. ${site.subdomain}`);
                    log(`   URL: https://${site.subdomain}.puter.site`);
                    log(`   UID: ${site.uid || 'N/A'}`);
                    log(`   Directory: ${site.root_dir || site.dir || 'N/A'}`);
                    log('---');
                });

            } catch (error) {
                log('Error listing websites:', 'error');
                log(JSON.stringify(error, null, 2));
            }
        }

        // Auto-run inspection when page loads
        window.addEventListener('load', () => {
            setTimeout(() => {
                log('Page loaded. Ready for testing!');
                log('Click "Inspect test-4xv4g39ud" to start debugging the delete issue.');
            }, 1000);
        });
    </script>
</body>
</html>