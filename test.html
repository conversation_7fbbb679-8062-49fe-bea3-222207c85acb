<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Puter.js Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 1.5rem;
            margin: 1rem 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            margin: 0.25rem;
        }
        button:hover {
            background: #0056b3;
        }
        .output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 1rem;
            margin: 1rem 0;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .error {
            color: #dc3545;
        }
        .success {
            color: #28a745;
        }
    </style>
</head>
<body>
    <h1>Puter.js API Test</h1>
    
    <div class="test-section">
        <h2>Hosting Tests</h2>
        <button onclick="testHostingList()">List Websites</button>
        <button onclick="testCreateWebsite()">Create Test Website</button>
        <button onclick="testDeleteWebsite()">Delete Test Website</button>
        <div id="hosting-output" class="output"></div>
    </div>
    
    <div class="test-section">
        <h2>File System Tests</h2>
        <button onclick="testFileList()">List Files</button>
        <button onclick="testCreateFile()">Create Test File</button>
        <button onclick="testDeleteFile()">Delete Test File</button>
        <div id="fs-output" class="output"></div>
    </div>
    
    <div class="test-section">
        <h2>Debug Information</h2>
        <button onclick="showDebugInfo()">Show Debug Info</button>
        <div id="debug-output" class="output"></div>
    </div>

    <script src="https://js.puter.com/v2/"></script>
    <script>
        let testWebsiteName = null;
        let testFileName = null;

        function log(elementId, message, type = 'info') {
            const output = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : '';
            output.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            output.scrollTop = output.scrollHeight;
        }

        async function testHostingList() {
            const output = document.getElementById('hosting-output');
            output.innerHTML = '';
            
            try {
                log('hosting-output', 'Testing puter.hosting.list()...');
                const websites = await puter.hosting.list();
                log('hosting-output', `Success! Found ${websites.length} websites:`, 'success');
                log('hosting-output', JSON.stringify(websites, null, 2));
            } catch (error) {
                log('hosting-output', `Error: ${error.message}`, 'error');
                console.error('Hosting list error:', error);
            }
        }

        async function testCreateWebsite() {
            const output = document.getElementById('hosting-output');
            
            try {
                testWebsiteName = 'test-' + Math.random().toString(36).substr(2, 9);
                log('hosting-output', `Creating test website: ${testWebsiteName}`);
                
                // Create directory
                const dirName = `test-dir-${Date.now()}`;
                await puter.fs.mkdir(dirName);
                log('hosting-output', `Created directory: ${dirName}`);
                
                // Create index.html
                const htmlContent = `<!DOCTYPE html>
<html><head><title>Test Site</title></head>
<body><h1>Test Website</h1><p>Created at ${new Date()}</p></body></html>`;
                
                await puter.fs.write(`${dirName}/index.html`, htmlContent);
                log('hosting-output', 'Created index.html file');
                
                // Create hosting
                const site = await puter.hosting.create(testWebsiteName, dirName);
                log('hosting-output', `Website created successfully!`, 'success');
                log('hosting-output', JSON.stringify(site, null, 2));
                
            } catch (error) {
                log('hosting-output', `Error creating website: ${error.message}`, 'error');
                console.error('Create website error:', error);
            }
        }

        async function testDeleteWebsite() {
            if (!testWebsiteName) {
                log('hosting-output', 'No test website to delete. Create one first.', 'error');
                return;
            }
            
            try {
                log('hosting-output', `Deleting test website: ${testWebsiteName}`);
                const result = await puter.hosting.delete(testWebsiteName);
                log('hosting-output', `Website deleted successfully!`, 'success');
                log('hosting-output', JSON.stringify(result, null, 2));
                testWebsiteName = null;
            } catch (error) {
                log('hosting-output', `Error deleting website: ${error.message}`, 'error');
                console.error('Delete website error:', error);
            }
        }

        async function testFileList() {
            const output = document.getElementById('fs-output');
            output.innerHTML = '';
            
            try {
                log('fs-output', 'Testing puter.fs.readdir()...');
                const files = await puter.fs.readdir('./');
                log('fs-output', `Success! Found ${files.length} items:`, 'success');
                files.forEach(file => {
                    log('fs-output', `${file.is_dir ? '[DIR]' : '[FILE]'} ${file.name} (${file.size || 0} bytes)`);
                });
            } catch (error) {
                log('fs-output', `Error: ${error.message}`, 'error');
                console.error('File list error:', error);
            }
        }

        async function testCreateFile() {
            try {
                testFileName = 'test-file-' + Math.random().toString(36).substr(2, 9) + '.txt';
                log('fs-output', `Creating test file: ${testFileName}`);
                
                const content = `Test file created at ${new Date().toISOString()}`;
                await puter.fs.write(testFileName, content);
                
                log('fs-output', `File created successfully!`, 'success');
            } catch (error) {
                log('fs-output', `Error creating file: ${error.message}`, 'error');
                console.error('Create file error:', error);
            }
        }

        async function testDeleteFile() {
            if (!testFileName) {
                log('fs-output', 'No test file to delete. Create one first.', 'error');
                return;
            }
            
            try {
                log('fs-output', `Deleting test file: ${testFileName}`);
                await puter.fs.delete(testFileName);
                log('fs-output', `File deleted successfully!`, 'success');
                testFileName = null;
            } catch (error) {
                log('fs-output', `Error deleting file: ${error.message}`, 'error');
                console.error('Delete file error:', error);
            }
        }

        function showDebugInfo() {
            const output = document.getElementById('debug-output');
            output.innerHTML = '';
            
            log('debug-output', 'Browser Information:');
            log('debug-output', `User Agent: ${navigator.userAgent}`);
            log('debug-output', `Platform: ${navigator.platform}`);
            log('debug-output', `Language: ${navigator.language}`);
            log('debug-output', `Online: ${navigator.onLine}`);
            log('debug-output', `Cookies Enabled: ${navigator.cookieEnabled}`);
            
            log('debug-output', '\nPuter.js Information:');
            log('debug-output', `Puter object: ${typeof puter}`);
            if (typeof puter !== 'undefined') {
                log('debug-output', `Puter.fs: ${typeof puter.fs}`);
                log('debug-output', `Puter.hosting: ${typeof puter.hosting}`);
            }
            
            log('debug-output', '\nWindow Location:');
            log('debug-output', `Protocol: ${window.location.protocol}`);
            log('debug-output', `Host: ${window.location.host}`);
            log('debug-output', `Pathname: ${window.location.pathname}`);
        }

        // Auto-run debug info on load
        window.addEventListener('load', () => {
            setTimeout(showDebugInfo, 1000);
        });
    </script>
</body>
</html>
