# Puter Cloud Manager

A beautiful, responsive web application for managing Puter.js hosting and cloud storage with a modern interface and dark/light mode support.

## Features

### 🌐 Website Hosting
- **Create Hosting**: Create new websites with custom subdomains
- **Manage Websites**: View, edit, and delete hosted websites
- **Live Preview**: Visit your websites directly from the dashboard
- **Content Editor**: Built-in editor for updating website content

### ☁️ Cloud Storage
- **File Upload**: Drag & drop or browse to upload files
- **File Management**: View, share, and delete uploaded files
- **Share Files**: Generate public URLs for file sharing
- **File Information**: View file size, type, and modification date

### 🎨 User Interface
- **Responsive Design**: Works perfectly on desktop, tablet, and mobile
- **Dark/Light Mode**: Toggle between themes with smooth transitions
- **Modern UI**: Clean, minimalist design with smooth animations
- **Error Handling**: Comprehensive error handling and user feedback

## Installation

1. **Clone or download** the project files to your web server
2. **Ensure PHP support** (optional, for additional API features)
3. **Open index.html** in your web browser

## File Structure

```
puter-cloud-manager/
├── index.html          # Main HTML file
├── styles.css          # CSS styles with theme support
├── script.js           # JavaScript functionality
├── api.php            # PHP API endpoints (optional)
└── README.md          # This file
```

## Usage

### Getting Started

1. Open `index.html` in your web browser
2. The application will automatically load with the Hosting section active
3. Use the navigation buttons to switch between Hosting and Cloud Storage

### Creating a Website

1. Go to the **Hosting** section
2. Enter a unique subdomain name
3. Add your HTML, CSS, and JavaScript content
4. Click **Create Website**
5. Your website will be available at `https://your-subdomain.puter.site`

### Managing Websites

- **Visit**: Opens your website in a new tab
- **Edit**: Opens a modal to edit the website content
- **Delete**: Permanently removes the website (with confirmation)

### Uploading Files

1. Go to the **Cloud Storage** section
2. Drag & drop files onto the upload area or click to browse
3. Click **Upload Files** to upload to Puter cloud storage

### Managing Files

- **Share**: Generates a public URL and copies it to clipboard
- **Delete**: Permanently removes the file (with confirmation)

## Technical Details

### Dependencies

- **Puter.js v2**: Cloud storage and hosting API
- **Font Awesome 6**: Icons
- **Modern CSS**: CSS Grid, Flexbox, CSS Variables
- **Vanilla JavaScript**: No additional frameworks required

### Browser Support

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

### API Endpoints (PHP)

The optional PHP API provides additional functionality:

- `GET /api.php/health` - Health check
- `GET /api.php/info` - Application information
- `POST /api.php/validate-subdomain` - Subdomain validation
- `POST /api.php/generate-share-url` - Generate file share URLs

## Customization

### Themes

The application supports custom themes through CSS variables. You can modify the theme colors in `styles.css`:

```css
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    /* ... other variables */
}
```

### Adding Features

The modular structure makes it easy to add new features:

1. Add HTML structure to `index.html`
2. Add styles to `styles.css`
3. Add functionality to `script.js`

## Error Handling

The application includes comprehensive error handling:

- **Network Errors**: Automatic retry and user feedback
- **Validation Errors**: Real-time form validation
- **API Errors**: Detailed error messages
- **Loading States**: Visual feedback during operations

## Security Considerations

- All user inputs are sanitized
- CORS headers are properly configured
- File uploads are handled securely through Puter.js
- No sensitive data is stored locally

## Performance

- **Lazy Loading**: Content is loaded on demand
- **Optimized Assets**: Minimal external dependencies
- **Efficient DOM Updates**: Minimal reflows and repaints
- **Responsive Images**: Proper image handling

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is open source and available under the MIT License.

## Support

For issues and questions:

1. Check the browser console for error messages
2. Ensure you have a stable internet connection
3. Verify Puter.js API availability
4. Check file permissions for PHP features

## Changelog

### Version 1.0.0
- Initial release
- Website hosting management
- Cloud storage functionality
- Dark/light theme support
- Responsive design
- Error handling and validation
