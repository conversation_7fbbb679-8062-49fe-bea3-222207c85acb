create a web based application in html css js and php inwhcih it uses puter.js as per documentation

i want a some specific functions in this application.

CREATE HOSTING - it ask for subdomain , content of website ( in html css js ) and a button to trigger that create hosting function .

-  after creation of hosting it must display information regarding hosted website like : subdomain id , subdomain , complete link , directory of hosted website , etc.

MANAGE HOSTING  - it will display hosted websites with 3 action buttons :

-  'visit' : when we click on it it will redirect to that specific website. 

- 'delete' : when we click on it it must delete that specific hosted website permanently.

- 'edit' : when we click on it it will lead us to a form in which we have to enter a new content for existed website (in html css js ).

CLOUD STORAGE - a upload form in which we have upload files and then it will store to cloud storage by puter.js .

- it must have 2 action buttons :

1 share - a button which can generate public url to that specific file which we uploded to cloud and also have a compy button , whenever we click on it it cill actomaticly write a that url in users clipboard

2 delete - a button that when we click on it , it will permanently delete that perticular file from cloud storage .

a website must able to handel errors and expections as abug , and the design of website must be a beautifull and minimale with a button to toggle between dark and light mode . the deisgn must be responcive and dynamic and animated 