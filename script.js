// Global variables
let currentEditingSite = null;
let uploadedFiles = [];
let hostedWebsites = [];

// DOM Elements
const themeToggle = document.getElementById('theme-toggle');
const navButtons = document.querySelectorAll('.nav-btn');
const sections = document.querySelectorAll('.section');
const createHostingForm = document.getElementById('create-hosting-form');
const uploadForm = document.getElementById('upload-form');
const editModal = document.getElementById('edit-modal');
const editForm = document.getElementById('edit-form');
const subdomainInput = document.getElementById('subdomain');
const subdomainPreview = document.getElementById('subdomain-preview');
const uploadArea = document.getElementById('upload-area');
const fileInput = document.getElementById('file-input');

// Initialize app
document.addEventListener('DOMContentLoaded', function() {
    console.log('Puter Cloud Manager initializing...');
    console.log('Puter object available:', typeof puter !== 'undefined');

    initializeTheme();
    setupEventListeners();

    // Add a small delay to ensure Puter.js is fully loaded
    setTimeout(() => {
        console.log('Loading initial data...');
        loadHostedWebsites();
        loadUploadedFiles();
    }, 1000);
});

// Theme Management
function initializeTheme() {
    const savedTheme = localStorage.getItem('theme') || 'light';
    document.documentElement.setAttribute('data-theme', savedTheme);
    updateThemeIcon(savedTheme);
}

function toggleTheme() {
    const currentTheme = document.documentElement.getAttribute('data-theme');
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
    
    document.documentElement.setAttribute('data-theme', newTheme);
    localStorage.setItem('theme', newTheme);
    updateThemeIcon(newTheme);
}

function updateThemeIcon(theme) {
    const icon = themeToggle.querySelector('i');
    icon.className = theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
}

// Navigation
function setupEventListeners() {
    // Theme toggle
    themeToggle.addEventListener('click', toggleTheme);
    
    // Navigation
    navButtons.forEach(btn => {
        btn.addEventListener('click', () => switchSection(btn.dataset.section));
    });
    
    // Forms
    createHostingForm.addEventListener('submit', handleCreateHosting);
    uploadForm.addEventListener('submit', handleFileUpload);
    editForm.addEventListener('submit', handleEditWebsite);
    
    // Subdomain preview
    subdomainInput.addEventListener('input', updateSubdomainPreview);
    
    // Upload area
    setupUploadArea();
    
    // Modal
    setupModal();
}

function switchSection(sectionId) {
    // Update navigation
    navButtons.forEach(btn => btn.classList.remove('active'));
    document.querySelector(`[data-section="${sectionId}"]`).classList.add('active');
    
    // Update sections
    sections.forEach(section => section.classList.remove('active'));
    document.getElementById(sectionId).classList.add('active');
    
    // Load data for the section
    if (sectionId === 'hosting') {
        loadHostedWebsites();
    } else if (sectionId === 'storage') {
        loadUploadedFiles();
    }
}

function updateSubdomainPreview() {
    const value = subdomainInput.value || 'your-subdomain';
    subdomainPreview.textContent = value;

    // Real-time validation
    if (value && value !== 'your-subdomain') {
        validateSubdomainInput(value);
    }
}

function validateSubdomainInput(subdomain) {
    const errors = [];

    // Basic validation
    if (subdomain.length < 3) {
        errors.push('Subdomain must be at least 3 characters long');
    }

    if (subdomain.length > 63) {
        errors.push('Subdomain must be less than 64 characters long');
    }

    if (!/^[a-z0-9-]+$/.test(subdomain)) {
        errors.push('Subdomain can only contain lowercase letters, numbers, and hyphens');
    }

    if (/^-|-$/.test(subdomain)) {
        errors.push('Subdomain cannot start or end with a hyphen');
    }

    // Reserved subdomains
    const reserved = ['www', 'api', 'admin', 'mail', 'ftp', 'localhost', 'test', 'app', 'cdn'];
    if (reserved.includes(subdomain.toLowerCase())) {
        errors.push('This subdomain is reserved');
    }

    // Update UI with validation results
    const input = document.getElementById('subdomain');
    const existingError = input.parentNode.querySelector('.validation-error');

    if (existingError) {
        existingError.remove();
    }

    if (errors.length > 0) {
        input.style.borderColor = 'var(--danger-color)';
        const errorDiv = document.createElement('div');
        errorDiv.className = 'validation-error';
        errorDiv.style.color = 'var(--danger-color)';
        errorDiv.style.fontSize = '0.875rem';
        errorDiv.style.marginTop = '0.25rem';
        errorDiv.textContent = errors[0]; // Show first error
        input.parentNode.appendChild(errorDiv);
        return false;
    } else {
        input.style.borderColor = 'var(--success-color)';
        return true;
    }
}

// Hosting Functions
async function handleCreateHosting(e) {
    e.preventDefault();

    const formData = new FormData(e.target);
    const subdomain = formData.get('subdomain').trim().toLowerCase();
    const content = formData.get('content').trim();

    if (!subdomain || !content) {
        showToast('Please fill in all fields', 'error');
        return;
    }

    // Validate subdomain
    if (!validateSubdomainInput(subdomain)) {
        showToast('Please fix the subdomain errors before creating the website', 'error');
        return;
    }

    // Basic HTML validation
    if (!content.toLowerCase().includes('<html') && !content.toLowerCase().includes('<!doctype')) {
        if (!confirm('Your content doesn\'t appear to be a complete HTML document. Do you want to wrap it in a basic HTML structure?')) {
            return;
        }

        // Wrap content in basic HTML structure
        const wrappedContent = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${subdomain}</title>
</head>
<body>
    ${content}
</body>
</html>`;

        // Update the textarea with wrapped content
        document.getElementById('website-content').value = wrappedContent;
        content = wrappedContent;
    }

    try {
        showLoading('Creating website...');

        // Create directory for the website
        const dirName = `website-${subdomain}-${Date.now()}`;
        await puter.fs.mkdir(dirName);

        // Create index.html file with the content
        await puter.fs.write(`${dirName}/index.html`, content);

        // Create the hosting
        const site = await puter.hosting.create(subdomain, dirName);

        showToast(`Website created successfully! Available at: ${site.subdomain}.puter.site`, 'success');

        // Reset form and validation
        e.target.reset();
        updateSubdomainPreview();
        document.getElementById('subdomain').style.borderColor = 'var(--border-color)';

        // Reload websites list
        loadHostedWebsites();

    } catch (error) {
        console.error('Error creating website:', error);
        let errorMessage = 'Error creating website';

        if (error.message.includes('already exists')) {
            errorMessage = 'A website with this subdomain already exists';
        } else if (error.message.includes('invalid')) {
            errorMessage = 'Invalid subdomain name';
        } else if (error.message) {
            errorMessage = error.message;
        }

        showToast(errorMessage, 'error');
    } finally {
        hideLoading();
    }
}

async function loadHostedWebsites() {
    const container = document.getElementById('hosted-websites');
    const loading = document.getElementById('websites-loading');

    // Check if elements exist before trying to use them
    if (!container) {
        console.error('hosted-websites container not found');
        return;
    }

    try {
        if (loading) {
            loading.style.display = 'block';
        }

        // Get all hosted websites
        hostedWebsites = await puter.hosting.list();
        console.log('Loaded websites:', hostedWebsites);

        if (loading) {
            loading.style.display = 'none';
        }

        if (!hostedWebsites || hostedWebsites.length === 0) {
            container.innerHTML = '<div class="loading">No websites found. Create your first website above!</div>';
            return;
        }

        // Ensure hostedWebsites is an array
        if (!Array.isArray(hostedWebsites)) {
            console.error('hostedWebsites is not an array:', hostedWebsites);
            container.innerHTML = '<div class="loading">Error: Invalid data format received</div>';
            return;
        }
        
        // Render websites
        container.innerHTML = hostedWebsites.map((site, index) => {
            console.log(`Rendering site ${index}:`, site);

            // Extract directory path more carefully - handle both string and object formats
            let rootDir = null;
            if (site.root_dir) {
                if (typeof site.root_dir === 'string') {
                    rootDir = site.root_dir;
                } else if (typeof site.root_dir === 'object') {
                    // Handle object format: use path, name, or uid
                    rootDir = site.root_dir.path || site.root_dir.name || site.root_dir.uid || null;
                }
            } else if (site.dir && typeof site.dir === 'string') {
                rootDir = site.dir;
            } else if (site.directory && typeof site.directory === 'string') {
                rootDir = site.directory;
            }

            console.log(`Extracted rootDir for ${site.subdomain}:`, rootDir);

            const createdDate = site.created_at || site.created || site.date_created || new Date().toISOString();
            const hasDirectory = rootDir && rootDir !== 'null' && rootDir !== 'undefined' && rootDir !== '';

            return `
            <div class="website-item" data-subdomain="${site.subdomain}" data-root-dir="${rootDir || ''}" data-index="${index}">
                <h4>${site.subdomain}</h4>
                <p><strong>URL:</strong> <a href="https://${site.subdomain}.puter.site" target="_blank">https://${site.subdomain}.puter.site</a></p>
                <p><strong>Directory:</strong> ${hasDirectory ? rootDir : '<span style="color: #f59e0b;">⚠️ No directory set</span>'}</p>
                <p><strong>Created:</strong> ${new Date(createdDate).toLocaleDateString()}</p>
                ${!hasDirectory ? '<p style="color: #f59e0b; font-size: 0.875rem;"><i class="fas fa-info-circle"></i> This website needs a directory to display content. Click Edit to set one up.</p>' : ''}
                <div class="website-actions">
                    <button class="btn btn-primary btn-small" onclick="visitWebsite('${site.subdomain}')">
                        <i class="fas fa-external-link-alt"></i> Visit
                    </button>
                    <button class="btn btn-secondary btn-small" onclick="editWebsiteByIndex(${index})" title="${hasDirectory ? 'Edit website content' : 'Set up directory and edit content'}">
                        <i class="fas fa-${hasDirectory ? 'edit' : 'folder-plus'}"></i> ${hasDirectory ? 'Edit' : 'Setup & Edit'}
                    </button>
                    <button class="btn btn-danger btn-small" onclick="deleteWebsiteByIndex(${index})">
                        <i class="fas fa-trash"></i> Delete
                    </button>
                </div>
            </div>
        `;
        }).join('');
        
    } catch (error) {
        console.error('Error loading websites:', error);
        if (loading) {
            loading.style.display = 'none';
        }
        container.innerHTML = '<div class="loading">Error loading websites. Please try again.</div>';
        showToast('Error loading websites', 'error');
    }
}

function visitWebsite(subdomain) {
    window.open(`https://${subdomain}.puter.site`, '_blank');
}

// New functions using index to avoid string interpolation issues
function editWebsiteByIndex(index) {
    const site = hostedWebsites[index];
    if (!site) {
        showToast('Website not found', 'error');
        return;
    }

    console.log('Editing website by index:', index, site);

    // Extract directory path more carefully - handle both string and object formats
    let rootDir = null;
    if (site.root_dir) {
        if (typeof site.root_dir === 'string') {
            rootDir = site.root_dir;
        } else if (typeof site.root_dir === 'object') {
            // Handle object format: use path, name, or uid
            rootDir = site.root_dir.path || site.root_dir.name || site.root_dir.uid || null;
        }
    } else if (site.dir && typeof site.dir === 'string') {
        rootDir = site.dir;
    } else if (site.directory && typeof site.directory === 'string') {
        rootDir = site.directory;
    }

    console.log('Extracted directory for editing:', rootDir);

    editWebsite(site.subdomain, rootDir);
}

function deleteWebsiteByIndex(index) {
    const site = hostedWebsites[index];
    if (!site) {
        showToast('Website not found', 'error');
        return;
    }

    console.log('=== DELETE BY INDEX DEBUG ===');
    console.log('Index:', index);
    console.log('Site object:', site);
    console.log('Site subdomain:', site.subdomain);
    console.log('Site subdomain type:', typeof site.subdomain);
    console.log('All hostedWebsites:', hostedWebsites);

    deleteWebsite(site.subdomain);
}

async function editWebsite(subdomain, rootDir) {
    if (!rootDir || rootDir === 'null' || rootDir === 'undefined' || rootDir === '') {
        // Website has no directory - offer to create one
        const createDir = confirm(`This website "${subdomain}" has no directory associated with it.\n\nWould you like to create a new directory and set up the website files?`);

        if (!createDir) {
            showToast('Cannot edit website without a directory', 'warning');
            return;
        }

        try {
            showLoading('Creating directory and setting up website...');

            // Create a new directory for this website
            const newDirName = `website-${subdomain}-${Date.now()}`;
            await puter.fs.mkdir(newDirName);
            console.log(`Created new directory: ${newDirName}`);

            // Create a basic index.html file
            const basicContent = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${subdomain}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 { color: #333; }
        p { color: #666; line-height: 1.6; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Welcome to ${subdomain}</h1>
        <p>This is your new website! You can edit this content to customize your site.</p>
        <p>Add your own HTML, CSS, and JavaScript to make it unique.</p>
    </div>
</body>
</html>`;

            await puter.fs.write(`${newDirName}/index.html`, basicContent);
            console.log('Created index.html file');

            // Update the hosting to point to the new directory
            await puter.hosting.update(subdomain, newDirName);
            console.log(`Updated hosting to point to ${newDirName}`);

            // Update the current editing site info
            rootDir = newDirName;

            showToast('Directory created and website set up successfully!', 'success');

            // Reload the websites list to show the updated directory
            loadHostedWebsites();

        } catch (setupError) {
            console.error('Error setting up website directory:', setupError);
            showToast('Error setting up website directory', 'error');
            hideLoading();
            return;
        }
    }

    try {
        showLoading('Loading website content...');

        // Try to read the current content
        let content, text;
        try {
            content = await puter.fs.read(`${rootDir}/index.html`);
            text = await content.text();
            console.log('Successfully loaded index.html');
        } catch (readError) {
            console.warn('Could not read index.html, trying alternative approaches:', readError);

            // Try alternative file names
            const alternatives = ['index.htm', 'default.html', 'home.html'];
            let found = false;

            for (const alt of alternatives) {
                try {
                    content = await puter.fs.read(`${rootDir}/${alt}`);
                    text = await content.text();
                    found = true;
                    console.log(`Found alternative file: ${alt}`);
                    break;
                } catch (altError) {
                    continue;
                }
            }

            if (!found) {
                // If no file found, create a basic template
                text = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${subdomain}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 { color: #333; }
        p { color: #666; line-height: 1.6; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Welcome to ${subdomain}</h1>
        <p>Edit this content to customize your website.</p>
        <p>You can add HTML, CSS, and JavaScript here.</p>
    </div>
</body>
</html>`;

                // Create the index.html file in the directory
                try {
                    await puter.fs.write(`${rootDir}/index.html`, text);
                    showToast('Created new index.html file for editing', 'info');
                } catch (writeError) {
                    console.error('Could not create index.html:', writeError);
                    showToast('No index.html found. Using template for editing.', 'info');
                }
            }
        }

        // Set up the edit modal
        currentEditingSite = { subdomain, rootDir };
        document.getElementById('edit-content').value = text;
        editModal.classList.add('active');

    } catch (error) {
        console.error('Error loading website content:', error);
        console.error('Error type:', typeof error);
        console.error('Error properties:', Object.keys(error));

        let errorMessage = 'Error loading website content';
        if (error && typeof error === 'object') {
            const message = error.message || error.error || error.description || error.toString();
            if (message && message !== '[object Object]') {
                errorMessage = `Error loading website content: ${message}`;
            }
        } else if (typeof error === 'string') {
            errorMessage = `Error loading website content: ${error}`;
        }

        showToast(errorMessage, 'error');
    } finally {
        hideLoading();
    }
}

async function handleEditWebsite(e) {
    e.preventDefault();

    if (!currentEditingSite) {
        showToast('No website selected for editing', 'error');
        return;
    }

    const newContent = document.getElementById('edit-content').value.trim();

    if (!newContent) {
        showToast('Content cannot be empty', 'error');
        return;
    }

    console.log('Saving website content:', {
        subdomain: currentEditingSite.subdomain,
        rootDir: currentEditingSite.rootDir,
        contentLength: newContent.length
    });

    try {
        showLoading('Updating website...');

        // Validate that we have a proper directory
        if (!currentEditingSite.rootDir || currentEditingSite.rootDir === 'null' || currentEditingSite.rootDir === 'undefined') {
            throw new Error('No valid directory found for this website. Please try editing again to set up the directory.');
        }

        // Check if directory exists first
        try {
            await puter.fs.stat(currentEditingSite.rootDir);
            console.log('Directory exists, proceeding with file write');
        } catch (statError) {
            console.warn('Directory may not exist, trying to create it:', statError);
            try {
                await puter.fs.mkdir(currentEditingSite.rootDir);
                console.log('Created missing directory');
            } catch (mkdirError) {
                console.error('Could not create directory:', mkdirError);
                throw new Error(`Directory "${currentEditingSite.rootDir}" does not exist and could not be created.`);
            }
        }

        // Update the index.html file
        const filePath = `${currentEditingSite.rootDir}/index.html`;
        console.log('Writing to file path:', filePath);

        await puter.fs.write(filePath, newContent);
        console.log('File written successfully');

        showToast('Website updated successfully!', 'success');
        editModal.classList.remove('active');
        currentEditingSite = null;

        // Reload the websites list to reflect any changes
        loadHostedWebsites();

    } catch (error) {
        console.error('Error updating website:', error);
        console.error('Error type:', typeof error);
        console.error('Error properties:', Object.keys(error));
        console.error('Current editing site:', currentEditingSite);

        let errorMessage = 'Error updating website';
        if (error && typeof error === 'object') {
            const message = error.message || error.error || error.description || error.toString();
            if (message && message !== '[object Object]') {
                if (message.includes('not found') || message.includes('Destination was not found')) {
                    errorMessage = `Directory "${currentEditingSite.rootDir}" was not found. The website may need to be set up again.`;
                } else {
                    errorMessage = `Error updating website: ${message}`;
                }
            }
        } else if (typeof error === 'string') {
            errorMessage = `Error updating website: ${error}`;
        }

        showToast(errorMessage, 'error');
    } finally {
        hideLoading();
    }
}

async function deleteWebsite(subdomain) {
    if (!subdomain || typeof subdomain !== 'string') {
        showToast('Invalid subdomain', 'error');
        return;
    }

    if (!confirm(`Are you sure you want to delete the website "${subdomain}"?\n\nThis will remove the subdomain from hosting and make the website inaccessible.\n\nThis action cannot be undone.`)) {
        return;
    }

    try {
        showLoading('Deleting website...');

        console.log(`=== DELETING WEBSITE: ${subdomain} ===`);
        console.log('Puter object available:', typeof puter !== 'undefined');
        console.log('Puter.hosting available:', typeof puter.hosting !== 'undefined');
        console.log('Puter.hosting.delete available:', typeof puter.hosting.delete !== 'undefined');

        // Step 1: Get the website details from the current list
        console.log('Step 1: Finding website in current list...');
        const allWebsites = await puter.hosting.list();
        console.log('All websites from API:', allWebsites);

        const targetWebsite = allWebsites.find(site => site.subdomain === subdomain);
        if (!targetWebsite) {
            throw new Error(`Website "${subdomain}" not found in current hosting list`);
        }

        console.log('Website found in list:', targetWebsite);
        const websiteUID = targetWebsite.uid;
        console.log('Website UID:', websiteUID);

        // Step 2: Try multiple delete approaches
        console.log('Step 2: Attempting delete with multiple approaches...');
        let deleteResult = null;
        let deleteSuccess = false;
        let deleteMethod = '';

        // Approach 1: Delete with subdomain (as per documentation)
        try {
            console.log('Approach 1: Deleting with subdomain...');
            deleteResult = await puter.hosting.delete(subdomain);
            deleteSuccess = true;
            deleteMethod = 'subdomain';
            console.log('✅ Delete with subdomain successful:', deleteResult);
        } catch (subdomainError) {
            console.log('❌ Delete with subdomain failed:', subdomainError);

            // Approach 2: Delete with UID (alternative approach)
            if (websiteUID) {
                try {
                    console.log('Approach 2: Deleting with UID...');
                    deleteResult = await puter.hosting.delete(websiteUID);
                    deleteSuccess = true;
                    deleteMethod = 'UID';
                    console.log('✅ Delete with UID successful:', deleteResult);
                } catch (uidError) {
                    console.log('❌ Delete with UID also failed:', uidError);

                    // Approach 3: Try trimmed subdomain (in case of whitespace issues)
                    try {
                        console.log('Approach 3: Deleting with trimmed subdomain...');
                        const trimmedSubdomain = subdomain.trim();
                        deleteResult = await puter.hosting.delete(trimmedSubdomain);
                        deleteSuccess = true;
                        deleteMethod = 'trimmed subdomain';
                        console.log('✅ Delete with trimmed subdomain successful:', deleteResult);
                    } catch (trimmedError) {
                        console.log('❌ All delete approaches failed');
                        // Re-throw the original subdomain error since that's the expected method
                        throw subdomainError;
                    }
                }
            } else {
                // No UID available, re-throw the original error
                throw subdomainError;
            }
        }

        if (!deleteSuccess) {
            throw new Error('All delete methods failed');
        }

        console.log('Delete operation completed successfully');
        console.log('Delete result:', deleteResult);
        console.log('Delete result type:', typeof deleteResult);

        // According to docs, should return true on success
        if (deleteResult === true) {
            showToast(`Website "${subdomain}" deleted successfully using ${deleteMethod}!`, 'success');
            console.log(`✅ Delete successful using method: ${deleteMethod}`);
        } else {
            console.warn('Unexpected delete result:', deleteResult);
            showToast(`Website "${subdomain}" may have been deleted using ${deleteMethod} (unexpected result: ${deleteResult})`, 'warning');
        }

        // Step 3: Verify the deletion worked
        console.log('Step 3: Verifying deletion...');
        try {
            const websitesAfterDelete = await puter.hosting.list();
            const stillExists = websitesAfterDelete.find(site => site.subdomain === subdomain);

            if (stillExists) {
                console.warn('⚠️ WARNING: Website still appears in list after delete!');
                showToast(`Warning: Website may still exist after delete`, 'warning');
            } else {
                console.log('✅ Deletion verified - website no longer in list');
            }
        } catch (verifyError) {
            console.warn('Could not verify deletion:', verifyError);
        }

        // Reload the websites list
        await loadHostedWebsites();

    } catch (error) {
        console.error('=== DELETE ERROR ===');
        console.error('Error object:', error);
        console.error('Error type:', typeof error);
        console.error('Error constructor:', error?.constructor?.name);
        console.error('Error message:', error?.message);
        console.error('Error stack:', error?.stack);

        // Log all properties of the error object
        console.error('All error properties:', Object.keys(error));
        console.error('Error values:', Object.values(error));
        console.error('Error entries:', Object.entries(error));

        // Try to stringify the entire error object
        try {
            console.error('Error JSON:', JSON.stringify(error, null, 2));
        } catch (stringifyError) {
            console.error('Could not stringify error:', stringifyError);
        }

        // Extract meaningful error message from various possible properties
        let errorMessage = 'Failed to delete website';

        // Function to safely extract error message from Puter.js API errors
        function extractErrorMessage(err) {
            // Handle Puter.js API error format: { success: false, error: { ... } }
            if (err && typeof err === 'object' && err.success === false && err.error) {
                const apiError = err.error;

                // Check for specific error codes
                if (apiError.code === 'entity_not_found') {
                    return `Website not found (${apiError.code})`;
                }

                // Extract message from the nested error
                if (apiError.message && typeof apiError.message === 'string') {
                    return apiError.message;
                }

                // Use error code if available
                if (apiError.code && typeof apiError.code === 'string') {
                    return `API Error: ${apiError.code}`;
                }

                // Use status if available
                if (apiError.status) {
                    return `HTTP ${apiError.status} Error`;
                }
            }

            // Check standard error properties
            if (err?.message && typeof err.message === 'string') {
                return err.message;
            }
            if (err?.error && typeof err.error === 'string') {
                return err.error;
            }
            if (err?.description && typeof err.description === 'string') {
                return err.description;
            }
            if (err?.detail && typeof err.detail === 'string') {
                return err.detail;
            }
            if (err?.reason && typeof err.reason === 'string') {
                return err.reason;
            }

            // Check HTTP-style properties
            if (err?.statusText && typeof err.statusText === 'string') {
                return `HTTP ${err.status || 'Error'}: ${err.statusText}`;
            }
            if (err?.responseText && typeof err.responseText === 'string') {
                return err.responseText;
            }

            // Check for nested error objects
            if (err?.error && typeof err.error === 'object') {
                return extractErrorMessage(err.error);
            }

            // Try to find any meaningful string in the object
            if (err && typeof err === 'object') {
                const allValues = Object.values(err);
                const stringValues = allValues.filter(val => typeof val === 'string' && val.length > 0);
                if (stringValues.length > 0) {
                    return stringValues[0];
                }

                // Look for nested objects that might contain error info
                const objectValues = allValues.filter(val => val && typeof val === 'object');
                for (const obj of objectValues) {
                    const nestedMessage = extractErrorMessage(obj);
                    if (nestedMessage !== 'Unknown error') {
                        return nestedMessage;
                    }
                }
            }

            return 'Unknown error';
        }

        const extractedMessage = extractErrorMessage(error);

        if (extractedMessage !== 'Unknown error') {
            errorMessage = `Delete failed: ${extractedMessage}`;
        } else if (typeof error === 'string') {
            errorMessage = `Delete failed: ${error}`;
        } else {
            errorMessage = `Delete failed: Unable to extract error details. Check console for raw error object.`;
        }

        // Check for common error patterns and handle "entity_not_found" specifically
        if (errorMessage.includes('not found') || errorMessage.includes('does not exist') ||
            errorMessage.includes('entity_not_found') || extractedMessage.includes('Website not found')) {
            errorMessage = `Website "${subdomain}" not found. It may have already been deleted.`;
            showToast(errorMessage, 'warning');
            // Reload the list in case it was deleted elsewhere
            await loadHostedWebsites();
            return; // Exit early for "not found" errors
        }

        showToast(errorMessage, 'error');
    } finally {
        hideLoading();
    }
}

// File Upload Functions
function setupUploadArea() {
    uploadArea.addEventListener('click', () => fileInput.click());
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('dragleave', handleDragLeave);
    uploadArea.addEventListener('drop', handleDrop);
    fileInput.addEventListener('change', handleFileSelect);
}

function handleDragOver(e) {
    e.preventDefault();
    uploadArea.classList.add('dragover');
}

function handleDragLeave(e) {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
}

function handleDrop(e) {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
    const files = Array.from(e.dataTransfer.files);
    uploadFiles(files);
}

function handleFileSelect(e) {
    const files = Array.from(e.target.files);
    uploadFiles(files);
}

async function handleFileUpload(e) {
    e.preventDefault();
    const files = Array.from(fileInput.files);
    
    if (files.length === 0) {
        showToast('Please select files to upload', 'warning');
        return;
    }
    
    uploadFiles(files);
}

async function uploadFiles(files) {
    if (files.length === 0) return;
    
    try {
        showLoading(`Uploading ${files.length} file(s)...`);
        
        // Upload files to Puter
        const uploadedFiles = await puter.fs.upload(files);
        
        showToast(`Successfully uploaded ${files.length} file(s)!`, 'success');
        
        // Reset file input
        fileInput.value = '';
        
        // Reload files list
        loadUploadedFiles();
        
    } catch (error) {
        console.error('Error uploading files:', error);
        showToast('Error uploading files', 'error');
    } finally {
        hideLoading();
    }
}

async function loadUploadedFiles() {
    const container = document.getElementById('uploaded-files');
    const loading = document.getElementById('files-loading');
    
    try {
        loading.style.display = 'block';
        
        // Get all files in the root directory
        const files = await puter.fs.readdir('./');
        
        // Filter out directories and system files
        uploadedFiles = files.filter(item => !item.is_dir && !item.name.startsWith('.'));
        
        loading.style.display = 'none';
        
        if (uploadedFiles.length === 0) {
            container.innerHTML = '<div class="loading">No files found. Upload your first file above!</div>';
            return;
        }
        
        // Render files
        container.innerHTML = uploadedFiles.map((file, index) => `
            <div class="file-item" data-filename="${file.name}" data-index="${index}">
                <h4>${file.name}</h4>
                <p><strong>Size:</strong> ${formatFileSize(file.size || 0)}</p>
                <p><strong>Type:</strong> ${getFileType(file.name)}</p>
                <p><strong>Modified:</strong> ${new Date(file.modified || file.created || Date.now()).toLocaleDateString()}</p>
                <div class="file-actions">
                    <button class="btn btn-success btn-small" onclick="shareFileByIndex(${index})">
                        <i class="fas fa-share"></i> Share
                    </button>
                    <button class="btn btn-danger btn-small" onclick="deleteFileByIndex(${index})">
                        <i class="fas fa-trash"></i> Delete
                    </button>
                </div>
            </div>
        `).join('');
        
    } catch (error) {
        console.error('Error loading files:', error);
        loading.style.display = 'none';
        container.innerHTML = '<div class="loading">Error loading files. Please try again.</div>';
        showToast('Error loading files', 'error');
    }
}

// Index-based file functions to avoid string interpolation issues
function shareFileByIndex(index) {
    const file = uploadedFiles[index];
    if (!file) {
        showToast('File not found', 'error');
        return;
    }
    shareFile(file.name);
}

function deleteFileByIndex(index) {
    const file = uploadedFiles[index];
    if (!file) {
        showToast('File not found', 'error');
        return;
    }
    deleteFile(file.name);
}

async function shareFile(filename) {
    try {
        // Get file information first
        const fileInfo = await puter.fs.stat(filename);

        // Create a more robust share URL using file UID if available
        let shareUrl;
        if (fileInfo.uid) {
            shareUrl = `https://puter.com/file/${fileInfo.uid}`;
        } else {
            // Fallback to a simple share URL
            shareUrl = `https://puter.com/app/share/${encodeURIComponent(filename)}`;
        }

        // Try to use the modern clipboard API
        if (navigator.clipboard && window.isSecureContext) {
            await navigator.clipboard.writeText(shareUrl);
            showToast(`Share URL copied to clipboard!`, 'success');
        } else {
            // Fallback for older browsers or non-secure contexts
            const textArea = document.createElement('textarea');
            textArea.value = shareUrl;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                document.execCommand('copy');
                showToast(`Share URL copied to clipboard!`, 'success');
            } catch (err) {
                showToast(`Share URL: ${shareUrl}`, 'info');
            }

            document.body.removeChild(textArea);
        }

    } catch (error) {
        console.error('Error sharing file:', error);
        showToast('Error creating share URL', 'error');
    }
}

async function deleteFile(filename) {
    if (!confirm(`Are you sure you want to delete "${filename}"? This action cannot be undone.`)) {
        return;
    }
    
    try {
        showLoading('Deleting file...');
        
        await puter.fs.delete(filename);
        
        showToast('File deleted successfully!', 'success');
        loadUploadedFiles();
        
    } catch (error) {
        console.error('Error deleting file:', error);
        showToast('Error deleting file', 'error');
    } finally {
        hideLoading();
    }
}

// Utility Functions
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function getFileType(filename) {
    const ext = filename.split('.').pop().toLowerCase();
    const types = {
        'jpg': 'Image', 'jpeg': 'Image', 'png': 'Image', 'gif': 'Image', 'svg': 'Image',
        'pdf': 'PDF', 'doc': 'Document', 'docx': 'Document', 'txt': 'Text',
        'mp4': 'Video', 'avi': 'Video', 'mov': 'Video',
        'mp3': 'Audio', 'wav': 'Audio', 'flac': 'Audio',
        'zip': 'Archive', 'rar': 'Archive', '7z': 'Archive',
        'js': 'JavaScript', 'html': 'HTML', 'css': 'CSS', 'json': 'JSON'
    };
    return types[ext] || 'File';
}

// Modal Functions
function setupModal() {
    const modalCloses = document.querySelectorAll('.modal-close');
    modalCloses.forEach(close => {
        close.addEventListener('click', closeModal);
    });
    
    editModal.addEventListener('click', (e) => {
        if (e.target === editModal) closeModal();
    });
}

function closeModal() {
    editModal.classList.remove('active');
    currentEditingSite = null;
}

// Toast Functions
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    toast.innerHTML = `
        <div style="display: flex; align-items: center; gap: 0.5rem;">
            <i class="fas fa-${getToastIcon(type)}"></i>
            <span>${message}</span>
        </div>
    `;
    
    document.getElementById('toast-container').appendChild(toast);
    
    setTimeout(() => {
        toast.remove();
    }, 5000);
}

function getToastIcon(type) {
    const icons = {
        'success': 'check-circle',
        'error': 'exclamation-circle',
        'warning': 'exclamation-triangle',
        'info': 'info-circle'
    };
    return icons[type] || 'info-circle';
}

// Loading Functions
function showLoading(message = 'Loading...') {
    // Create loading overlay
    const overlay = document.createElement('div');
    overlay.id = 'loading-overlay';
    overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 2000;
        color: white;
        font-size: 1.2rem;
    `;
    overlay.innerHTML = `
        <div style="text-align: center;">
            <i class="fas fa-spinner fa-spin" style="font-size: 2rem; margin-bottom: 1rem;"></i>
            <div>${message}</div>
        </div>
    `;
    
    document.body.appendChild(overlay);
}

function hideLoading() {
    const overlay = document.getElementById('loading-overlay');
    if (overlay) {
        overlay.remove();
    }
}

// Debug functions (available in console)
window.debugPuter = {
    async testDelete(subdomain) {
        console.log('=== DEBUG DELETE TEST (Following Documentation) ===');
        console.log('Subdomain:', subdomain);
        console.log('Puter object:', typeof puter);
        console.log('Puter.hosting:', typeof puter.hosting);
        console.log('Puter.hosting.delete:', typeof puter.hosting.delete);

        if (!subdomain || typeof subdomain !== 'string') {
            console.error('Invalid subdomain - must be a non-empty string');
            return { success: false, error: 'Invalid subdomain' };
        }

        try {
            console.log('Calling puter.hosting.delete() as per documentation...');
            const result = await puter.hosting.delete(subdomain);
            console.log('Delete successful!');
            console.log('Result:', result);
            console.log('Result type:', typeof result);
            console.log('Result === true:', result === true);

            // According to docs, should return true
            if (result === true) {
                console.log('✅ Delete successful - returned true as expected');
                return { success: true, result };
            } else {
                console.log('⚠️ Delete completed but returned unexpected value');
                return { success: true, result, warning: 'Unexpected return value' };
            }
        } catch (error) {
            console.error('❌ Delete failed with error:', error);
            console.error('Error type:', typeof error);
            console.error('Error constructor:', error?.constructor?.name);
            console.error('Error properties:', Object.getOwnPropertyNames(error || {}));

            // According to docs: "If a subdomain with the given name does not exist, the promise will be rejected with an error"
            console.log('This error is expected if the subdomain does not exist');
            return { success: false, error };
        }
    },

    async forceDeleteWebsite(subdomain) {
        console.log(`=== FORCE DELETE: ${subdomain} ===`);

        if (!subdomain) {
            console.error('No subdomain provided');
            return { success: false, error: 'No subdomain provided' };
        }

        try {
            // First check if the website exists
            console.log('1. Checking if website exists...');
            let exists = false;
            try {
                const siteInfo = await puter.hosting.get(subdomain);
                console.log('Website exists:', siteInfo);
                exists = true;
            } catch (getError) {
                console.log('Website may not exist:', getError.message);
            }

            // Attempt to delete
            console.log('2. Attempting to delete...');
            const deleteResult = await puter.hosting.delete(subdomain);
            console.log('Delete result:', deleteResult);

            // Verify deletion
            console.log('3. Verifying deletion...');
            try {
                await puter.hosting.get(subdomain);
                console.log('WARNING: Website still exists after delete!');
                return { success: false, error: 'Website still exists after delete' };
            } catch (verifyError) {
                console.log('Deletion verified - website no longer exists');
                return { success: true, result: deleteResult };
            }

        } catch (error) {
            console.error('Force delete failed:', error);
            console.error('Error details:', {
                type: typeof error,
                constructor: error?.constructor?.name,
                keys: Object.keys(error),
                values: Object.values(error),
                json: JSON.stringify(error, null, 2)
            });
            return { success: false, error: error.message || error };
        }
    },

    // Quick test function to inspect error format
    async inspectDeleteError(subdomain) {
        console.log(`=== INSPECTING DELETE ERROR FOR: ${subdomain} ===`);

        try {
            const result = await puter.hosting.delete(subdomain);
            console.log('Unexpected success:', result);
            return { success: true, result };
        } catch (error) {
            console.log('=== DETAILED ERROR INSPECTION ===');
            console.log('Raw error:', error);
            console.log('Error type:', typeof error);
            console.log('Error constructor:', error?.constructor?.name);
            console.log('Error prototype:', Object.getPrototypeOf(error));
            console.log('Error keys:', Object.keys(error));
            console.log('Error values:', Object.values(error));
            console.log('Error entries:', Object.entries(error));

            // Check for common error properties
            const commonProps = ['message', 'error', 'description', 'detail', 'reason', 'status', 'code', 'statusText', 'responseText'];
            console.log('Common properties check:');
            commonProps.forEach(prop => {
                if (error.hasOwnProperty(prop)) {
                    console.log(`  ${prop}:`, error[prop]);
                }
            });

            // Try different ways to extract error info
            console.log('Extraction attempts:');
            console.log('  toString():', error.toString());
            console.log('  valueOf():', error.valueOf());

            try {
                console.log('  JSON.stringify():', JSON.stringify(error, null, 2));
            } catch (e) {
                console.log('  JSON.stringify() failed:', e.message);
            }

            return { success: false, error, analysis: 'Check console for detailed error inspection' };
        }
    },

    // Simple manual delete function for testing
    async manualDelete(subdomain) {
        console.log(`=== MANUAL DELETE TEST: ${subdomain} ===`);

        if (!subdomain) {
            console.error('No subdomain provided');
            return;
        }

        console.log('Step 1: Checking Puter.js availability...');
        console.log('- puter:', typeof puter);
        console.log('- puter.hosting:', typeof puter.hosting);
        console.log('- puter.hosting.delete:', typeof puter.hosting.delete);

        console.log('Step 2: Listing current websites...');
        try {
            const websites = await puter.hosting.list();
            console.log('Current websites:', websites);
            const targetSite = websites.find(site => site.subdomain === subdomain);
            if (targetSite) {
                console.log('Target website found:', targetSite);
            } else {
                console.log('Target website NOT found in list');
                return { success: false, error: 'Website not found in list' };
            }
        } catch (listError) {
            console.error('Failed to list websites:', listError);
        }

        console.log('Step 3: Attempting delete...');
        try {
            const result = await puter.hosting.delete(subdomain);
            console.log('Delete result:', result);
            console.log('Delete successful!');

            // Verify deletion
            console.log('Step 4: Verifying deletion...');
            const websitesAfter = await puter.hosting.list();
            const stillExists = websitesAfter.find(site => site.subdomain === subdomain);
            if (stillExists) {
                console.log('WARNING: Website still exists after delete!');
                return { success: false, error: 'Website still exists after delete' };
            } else {
                console.log('Deletion verified - website no longer in list');
                return { success: true, result };
            }

        } catch (error) {
            console.error('Delete failed with error:', error);
            console.error('Error details:', {
                type: typeof error,
                constructor: error?.constructor?.name,
                message: error?.message,
                keys: Object.keys(error || {}),
                values: Object.values(error || {}),
                stringified: JSON.stringify(error, null, 2)
            });
            return { success: false, error };
        }
    },

    async listWebsites() {
        console.log('=== DEBUG LIST WEBSITES ===');
        try {
            const websites = await puter.hosting.list();
            console.log('Websites:', websites);
            return websites;
        } catch (error) {
            console.error('List failed:', error);
            throw error;
        }
    },

    getHostedWebsites() {
        console.log('Current hostedWebsites array:', hostedWebsites);
        return hostedWebsites;
    },

    checkWebsiteDirectories() {
        console.log('=== WEBSITE DIRECTORY CHECK ===');
        if (!hostedWebsites || hostedWebsites.length === 0) {
            console.log('No websites found');
            return;
        }

        hostedWebsites.forEach((site, index) => {
            console.log(`${index + 1}. ${site.subdomain}:`);
            console.log('   - Full site object:', site);
            console.log(`   - root_dir: ${site.root_dir} (type: ${typeof site.root_dir})`);
            console.log(`   - dir: ${site.dir} (type: ${typeof site.dir})`);
            console.log(`   - directory: ${site.directory} (type: ${typeof site.directory})`);

            // Check if root_dir is an object
            if (site.root_dir && typeof site.root_dir === 'object') {
                console.log('   - root_dir object properties:', Object.keys(site.root_dir));
                console.log('   - root_dir.path:', site.root_dir.path);
                console.log('   - root_dir.name:', site.root_dir.name);
            }

            // Extract directory using our logic
            let rootDir = null;
            if (site.root_dir && typeof site.root_dir === 'string') {
                rootDir = site.root_dir;
            } else if (site.dir && typeof site.dir === 'string') {
                rootDir = site.dir;
            } else if (site.directory && typeof site.directory === 'string') {
                rootDir = site.directory;
            } else if (site.root_dir && typeof site.root_dir === 'object' && site.root_dir.path) {
                rootDir = site.root_dir.path;
            }

            const hasDirectory = rootDir && rootDir !== 'null' && rootDir !== 'undefined' && rootDir !== '';

            console.log(`   - extracted rootDir: ${rootDir}`);
            console.log(`   - hasDirectory: ${hasDirectory}`);
            console.log('   - all properties:', Object.keys(site));
            console.log('---');
        });
    },

    async fixWebsiteDirectory(subdomain) {
        console.log(`=== FIXING DIRECTORY FOR ${subdomain} ===`);
        try {
            // Create a new directory
            const newDirName = `website-${subdomain}-${Date.now()}`;
            await puter.fs.mkdir(newDirName);
            console.log(`Created directory: ${newDirName}`);

            // Create basic content
            const content = `<!DOCTYPE html>
<html><head><title>${subdomain}</title></head>
<body><h1>Welcome to ${subdomain}</h1><p>Website set up successfully!</p></body></html>`;

            await puter.fs.write(`${newDirName}/index.html`, content);
            console.log('Created index.html');

            // Update hosting
            const result = await puter.hosting.update(subdomain, newDirName);
            console.log('Updated hosting:', result);

            return { success: true, directory: newDirName };
        } catch (error) {
            console.error('Error fixing directory:', error);
            return { success: false, error };
        }
    },

    // Test function that follows the exact documentation example
    async docExampleDelete(subdomain) {
        console.log('=== DOCUMENTATION EXAMPLE DELETE ===');
        console.log('Following the exact pattern from basic documentation.txt');

        if (!subdomain) {
            console.error('No subdomain provided');
            return { success: false, error: 'No subdomain provided' };
        }

        try {
            // This follows the exact pattern from the documentation:
            // const site2 = await puter.hosting.delete(site.subdomain);
            console.log(`Deleting subdomain: ${subdomain}`);
            const deleteResult = await puter.hosting.delete(subdomain);
            console.log('Delete result:', deleteResult);

            // The documentation shows it should work without any special handling
            console.log('✅ Delete completed successfully');
            return { success: true, result: deleteResult };

        } catch (error) {
            console.error('❌ Delete failed:', error);

            // Check if it's the expected "subdomain does not exist" error
            if (error && (
                error.message?.includes('not exist') ||
                error.message?.includes('not found') ||
                error.toString().includes('not exist') ||
                error.toString().includes('not found')
            )) {
                console.log('This appears to be a "subdomain does not exist" error');
                return { success: false, error: 'Subdomain does not exist', type: 'not_found' };
            }

            return { success: false, error, type: 'unknown' };
        }
    },

    // Test the fixed error handling
    async testErrorHandling() {
        console.log('=== TESTING ERROR HANDLING ===');

        // Test with a non-existent subdomain
        const fakeSubdomain = 'definitely-does-not-exist-' + Math.random().toString(36).substr(2, 9);
        console.log(`Testing with fake subdomain: ${fakeSubdomain}`);

        try {
            const result = await puter.hosting.delete(fakeSubdomain);
            console.log('Unexpected success:', result);
            return { success: true, result, note: 'This should not happen' };
        } catch (error) {
            console.log('Expected error caught:', error);

            // Test our error extraction function
            const extractedMessage = this.extractPuterError(error);
            console.log('Extracted message:', extractedMessage);

            return {
                success: false,
                error,
                extractedMessage,
                isEntityNotFound: error?.error?.code === 'entity_not_found'
            };
        }
    },

    // Helper function to extract Puter.js errors (same as in main code)
    extractPuterError(err) {
        if (err && typeof err === 'object' && err.success === false && err.error) {
            const apiError = err.error;

            if (apiError.code === 'entity_not_found') {
                return `Website not found (${apiError.code})`;
            }

            if (apiError.message && typeof apiError.message === 'string') {
                return apiError.message;
            }

            if (apiError.code && typeof apiError.code === 'string') {
                return `API Error: ${apiError.code}`;
            }

            if (apiError.status) {
                return `HTTP ${apiError.status} Error`;
            }
        }

        return err?.message || 'Unknown error';
    },

    // Debug function to inspect a specific website
    async inspectWebsite(subdomain) {
        console.log(`=== INSPECTING WEBSITE: ${subdomain} ===`);

        console.log('1. Checking with puter.hosting.get()...');
        try {
            const getResult = await puter.hosting.get(subdomain);
            console.log('✅ Found with get():', getResult);
        } catch (getError) {
            console.log('❌ Not found with get():', getError);
        }

        console.log('2. Checking with puter.hosting.list()...');
        try {
            const allSites = await puter.hosting.list();
            console.log('All sites:', allSites);

            const foundSite = allSites.find(site => site.subdomain === subdomain);
            if (foundSite) {
                console.log('✅ Found in list:', foundSite);
            } else {
                console.log('❌ Not found in list');

                // Check for similar subdomains
                const similar = allSites.filter(site =>
                    site.subdomain.includes(subdomain) || subdomain.includes(site.subdomain)
                );
                if (similar.length > 0) {
                    console.log('🔍 Similar subdomains found:', similar);
                }
            }
        } catch (listError) {
            console.log('❌ Error listing sites:', listError);
        }

        console.log('3. Checking if website is accessible...');
        const websiteUrl = `https://${subdomain}.puter.site`;
        console.log(`Website URL: ${websiteUrl}`);
        console.log('Try visiting this URL to see if it\'s actually live');

        console.log('4. Checking local hostedWebsites array...');
        if (window.hostedWebsites) {
            const localSite = window.hostedWebsites.find(site => site.subdomain === subdomain);
            if (localSite) {
                console.log('✅ Found in local array:', localSite);
            } else {
                console.log('❌ Not found in local array');
                console.log('Local array contents:', window.hostedWebsites);
            }
        } else {
            console.log('❌ Local hostedWebsites array not available');
        }

        return {
            subdomain,
            websiteUrl,
            note: 'Check console for detailed inspection results'
        };
    }
};
