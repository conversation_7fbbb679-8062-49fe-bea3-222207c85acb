// Global variables
let currentEditingSite = null;
let uploadedFiles = [];
let hostedWebsites = [];

// DOM Elements
const themeToggle = document.getElementById('theme-toggle');
const navButtons = document.querySelectorAll('.nav-btn');
const sections = document.querySelectorAll('.section');
const createHostingForm = document.getElementById('create-hosting-form');
const uploadForm = document.getElementById('upload-form');
const editModal = document.getElementById('edit-modal');
const editForm = document.getElementById('edit-form');
const subdomainInput = document.getElementById('subdomain');
const subdomainPreview = document.getElementById('subdomain-preview');
const uploadArea = document.getElementById('upload-area');
const fileInput = document.getElementById('file-input');

// Initialize app
document.addEventListener('DOMContentLoaded', function() {
    initializeTheme();
    setupEventListeners();
    loadHostedWebsites();
    loadUploadedFiles();
});

// Theme Management
function initializeTheme() {
    const savedTheme = localStorage.getItem('theme') || 'light';
    document.documentElement.setAttribute('data-theme', savedTheme);
    updateThemeIcon(savedTheme);
}

function toggleTheme() {
    const currentTheme = document.documentElement.getAttribute('data-theme');
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
    
    document.documentElement.setAttribute('data-theme', newTheme);
    localStorage.setItem('theme', newTheme);
    updateThemeIcon(newTheme);
}

function updateThemeIcon(theme) {
    const icon = themeToggle.querySelector('i');
    icon.className = theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
}

// Navigation
function setupEventListeners() {
    // Theme toggle
    themeToggle.addEventListener('click', toggleTheme);
    
    // Navigation
    navButtons.forEach(btn => {
        btn.addEventListener('click', () => switchSection(btn.dataset.section));
    });
    
    // Forms
    createHostingForm.addEventListener('submit', handleCreateHosting);
    uploadForm.addEventListener('submit', handleFileUpload);
    editForm.addEventListener('submit', handleEditWebsite);
    
    // Subdomain preview
    subdomainInput.addEventListener('input', updateSubdomainPreview);
    
    // Upload area
    setupUploadArea();
    
    // Modal
    setupModal();
}

function switchSection(sectionId) {
    // Update navigation
    navButtons.forEach(btn => btn.classList.remove('active'));
    document.querySelector(`[data-section="${sectionId}"]`).classList.add('active');
    
    // Update sections
    sections.forEach(section => section.classList.remove('active'));
    document.getElementById(sectionId).classList.add('active');
    
    // Load data for the section
    if (sectionId === 'hosting') {
        loadHostedWebsites();
    } else if (sectionId === 'storage') {
        loadUploadedFiles();
    }
}

function updateSubdomainPreview() {
    const value = subdomainInput.value || 'your-subdomain';
    subdomainPreview.textContent = value;

    // Real-time validation
    if (value && value !== 'your-subdomain') {
        validateSubdomainInput(value);
    }
}

function validateSubdomainInput(subdomain) {
    const errors = [];

    // Basic validation
    if (subdomain.length < 3) {
        errors.push('Subdomain must be at least 3 characters long');
    }

    if (subdomain.length > 63) {
        errors.push('Subdomain must be less than 64 characters long');
    }

    if (!/^[a-z0-9-]+$/.test(subdomain)) {
        errors.push('Subdomain can only contain lowercase letters, numbers, and hyphens');
    }

    if (/^-|-$/.test(subdomain)) {
        errors.push('Subdomain cannot start or end with a hyphen');
    }

    // Reserved subdomains
    const reserved = ['www', 'api', 'admin', 'mail', 'ftp', 'localhost', 'test', 'app', 'cdn'];
    if (reserved.includes(subdomain.toLowerCase())) {
        errors.push('This subdomain is reserved');
    }

    // Update UI with validation results
    const input = document.getElementById('subdomain');
    const existingError = input.parentNode.querySelector('.validation-error');

    if (existingError) {
        existingError.remove();
    }

    if (errors.length > 0) {
        input.style.borderColor = 'var(--danger-color)';
        const errorDiv = document.createElement('div');
        errorDiv.className = 'validation-error';
        errorDiv.style.color = 'var(--danger-color)';
        errorDiv.style.fontSize = '0.875rem';
        errorDiv.style.marginTop = '0.25rem';
        errorDiv.textContent = errors[0]; // Show first error
        input.parentNode.appendChild(errorDiv);
        return false;
    } else {
        input.style.borderColor = 'var(--success-color)';
        return true;
    }
}

// Hosting Functions
async function handleCreateHosting(e) {
    e.preventDefault();

    const formData = new FormData(e.target);
    const subdomain = formData.get('subdomain').trim().toLowerCase();
    const content = formData.get('content').trim();

    if (!subdomain || !content) {
        showToast('Please fill in all fields', 'error');
        return;
    }

    // Validate subdomain
    if (!validateSubdomainInput(subdomain)) {
        showToast('Please fix the subdomain errors before creating the website', 'error');
        return;
    }

    // Basic HTML validation
    if (!content.toLowerCase().includes('<html') && !content.toLowerCase().includes('<!doctype')) {
        if (!confirm('Your content doesn\'t appear to be a complete HTML document. Do you want to wrap it in a basic HTML structure?')) {
            return;
        }

        // Wrap content in basic HTML structure
        const wrappedContent = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${subdomain}</title>
</head>
<body>
    ${content}
</body>
</html>`;

        // Update the textarea with wrapped content
        document.getElementById('website-content').value = wrappedContent;
        content = wrappedContent;
    }

    try {
        showLoading('Creating website...');

        // Create directory for the website
        const dirName = `website-${subdomain}-${Date.now()}`;
        await puter.fs.mkdir(dirName);

        // Create index.html file with the content
        await puter.fs.write(`${dirName}/index.html`, content);

        // Create the hosting
        const site = await puter.hosting.create(subdomain, dirName);

        showToast(`Website created successfully! Available at: ${site.subdomain}.puter.site`, 'success');

        // Reset form and validation
        e.target.reset();
        updateSubdomainPreview();
        document.getElementById('subdomain').style.borderColor = 'var(--border-color)';

        // Reload websites list
        loadHostedWebsites();

    } catch (error) {
        console.error('Error creating website:', error);
        let errorMessage = 'Error creating website';

        if (error.message.includes('already exists')) {
            errorMessage = 'A website with this subdomain already exists';
        } else if (error.message.includes('invalid')) {
            errorMessage = 'Invalid subdomain name';
        } else if (error.message) {
            errorMessage = error.message;
        }

        showToast(errorMessage, 'error');
    } finally {
        hideLoading();
    }
}

async function loadHostedWebsites() {
    const container = document.getElementById('hosted-websites');
    const loading = document.getElementById('websites-loading');
    
    try {
        loading.style.display = 'block';
        
        // Get all hosted websites
        hostedWebsites = await puter.hosting.list();
        
        loading.style.display = 'none';
        
        if (hostedWebsites.length === 0) {
            container.innerHTML = '<div class="loading">No websites found. Create your first website above!</div>';
            return;
        }
        
        // Render websites
        container.innerHTML = hostedWebsites.map(site => `
            <div class="website-item">
                <h4>${site.subdomain}</h4>
                <p><strong>URL:</strong> <a href="https://${site.subdomain}.puter.site" target="_blank">https://${site.subdomain}.puter.site</a></p>
                <p><strong>Directory:</strong> ${site.root_dir || 'Not set'}</p>
                <p><strong>Created:</strong> ${new Date(site.created_at).toLocaleDateString()}</p>
                <div class="website-actions">
                    <button class="btn btn-primary btn-small" onclick="visitWebsite('${site.subdomain}')">
                        <i class="fas fa-external-link-alt"></i> Visit
                    </button>
                    <button class="btn btn-secondary btn-small" onclick="editWebsite('${site.subdomain}', '${site.root_dir}')">
                        <i class="fas fa-edit"></i> Edit
                    </button>
                    <button class="btn btn-danger btn-small" onclick="deleteWebsite('${site.subdomain}')">
                        <i class="fas fa-trash"></i> Delete
                    </button>
                </div>
            </div>
        `).join('');
        
    } catch (error) {
        console.error('Error loading websites:', error);
        loading.style.display = 'none';
        container.innerHTML = '<div class="loading">Error loading websites. Please try again.</div>';
        showToast('Error loading websites', 'error');
    }
}

function visitWebsite(subdomain) {
    window.open(`https://${subdomain}.puter.site`, '_blank');
}

async function editWebsite(subdomain, rootDir) {
    if (!rootDir) {
        showToast('This website has no directory to edit', 'warning');
        return;
    }
    
    try {
        // Read the current content
        const content = await puter.fs.read(`${rootDir}/index.html`);
        const text = await content.text();
        
        // Set up the edit modal
        currentEditingSite = { subdomain, rootDir };
        document.getElementById('edit-content').value = text;
        editModal.classList.add('active');
        
    } catch (error) {
        console.error('Error loading website content:', error);
        showToast('Error loading website content', 'error');
    }
}

async function handleEditWebsite(e) {
    e.preventDefault();
    
    if (!currentEditingSite) return;
    
    const newContent = document.getElementById('edit-content').value.trim();
    
    if (!newContent) {
        showToast('Content cannot be empty', 'error');
        return;
    }
    
    try {
        showLoading('Updating website...');
        
        // Update the index.html file
        await puter.fs.write(`${currentEditingSite.rootDir}/index.html`, newContent);
        
        showToast('Website updated successfully!', 'success');
        editModal.classList.remove('active');
        currentEditingSite = null;
        
    } catch (error) {
        console.error('Error updating website:', error);
        showToast('Error updating website', 'error');
    } finally {
        hideLoading();
    }
}

async function deleteWebsite(subdomain) {
    if (!confirm(`Are you sure you want to delete the website "${subdomain}"? This action cannot be undone.`)) {
        return;
    }
    
    try {
        showLoading('Deleting website...');
        
        // Delete the hosting
        await puter.hosting.delete(subdomain);
        
        showToast('Website deleted successfully!', 'success');
        loadHostedWebsites();
        
    } catch (error) {
        console.error('Error deleting website:', error);
        showToast('Error deleting website', 'error');
    } finally {
        hideLoading();
    }
}

// File Upload Functions
function setupUploadArea() {
    uploadArea.addEventListener('click', () => fileInput.click());
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('dragleave', handleDragLeave);
    uploadArea.addEventListener('drop', handleDrop);
    fileInput.addEventListener('change', handleFileSelect);
}

function handleDragOver(e) {
    e.preventDefault();
    uploadArea.classList.add('dragover');
}

function handleDragLeave(e) {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
}

function handleDrop(e) {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
    const files = Array.from(e.dataTransfer.files);
    uploadFiles(files);
}

function handleFileSelect(e) {
    const files = Array.from(e.target.files);
    uploadFiles(files);
}

async function handleFileUpload(e) {
    e.preventDefault();
    const files = Array.from(fileInput.files);
    
    if (files.length === 0) {
        showToast('Please select files to upload', 'warning');
        return;
    }
    
    uploadFiles(files);
}

async function uploadFiles(files) {
    if (files.length === 0) return;
    
    try {
        showLoading(`Uploading ${files.length} file(s)...`);
        
        // Upload files to Puter
        const uploadedFiles = await puter.fs.upload(files);
        
        showToast(`Successfully uploaded ${files.length} file(s)!`, 'success');
        
        // Reset file input
        fileInput.value = '';
        
        // Reload files list
        loadUploadedFiles();
        
    } catch (error) {
        console.error('Error uploading files:', error);
        showToast('Error uploading files', 'error');
    } finally {
        hideLoading();
    }
}

async function loadUploadedFiles() {
    const container = document.getElementById('uploaded-files');
    const loading = document.getElementById('files-loading');
    
    try {
        loading.style.display = 'block';
        
        // Get all files in the root directory
        const files = await puter.fs.readdir('./');
        
        // Filter out directories and system files
        uploadedFiles = files.filter(item => !item.is_dir && !item.name.startsWith('.'));
        
        loading.style.display = 'none';
        
        if (uploadedFiles.length === 0) {
            container.innerHTML = '<div class="loading">No files found. Upload your first file above!</div>';
            return;
        }
        
        // Render files
        container.innerHTML = uploadedFiles.map(file => `
            <div class="file-item">
                <h4>${file.name}</h4>
                <p><strong>Size:</strong> ${formatFileSize(file.size)}</p>
                <p><strong>Type:</strong> ${getFileType(file.name)}</p>
                <p><strong>Modified:</strong> ${new Date(file.modified).toLocaleDateString()}</p>
                <div class="file-actions">
                    <button class="btn btn-success btn-small" onclick="shareFile('${file.name}')">
                        <i class="fas fa-share"></i> Share
                    </button>
                    <button class="btn btn-danger btn-small" onclick="deleteFile('${file.name}')">
                        <i class="fas fa-trash"></i> Delete
                    </button>
                </div>
            </div>
        `).join('');
        
    } catch (error) {
        console.error('Error loading files:', error);
        loading.style.display = 'none';
        container.innerHTML = '<div class="loading">Error loading files. Please try again.</div>';
        showToast('Error loading files', 'error');
    }
}

async function shareFile(filename) {
    try {
        // Get file information first
        const fileInfo = await puter.fs.stat(filename);

        // Create a more robust share URL using file UID if available
        let shareUrl;
        if (fileInfo.uid) {
            shareUrl = `https://puter.com/file/${fileInfo.uid}`;
        } else {
            // Fallback to a simple share URL
            shareUrl = `https://puter.com/app/share/${encodeURIComponent(filename)}`;
        }

        // Try to use the modern clipboard API
        if (navigator.clipboard && window.isSecureContext) {
            await navigator.clipboard.writeText(shareUrl);
            showToast(`Share URL copied to clipboard!`, 'success');
        } else {
            // Fallback for older browsers or non-secure contexts
            const textArea = document.createElement('textarea');
            textArea.value = shareUrl;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                document.execCommand('copy');
                showToast(`Share URL copied to clipboard!`, 'success');
            } catch (err) {
                showToast(`Share URL: ${shareUrl}`, 'info');
            }

            document.body.removeChild(textArea);
        }

    } catch (error) {
        console.error('Error sharing file:', error);
        showToast('Error creating share URL', 'error');
    }
}

async function deleteFile(filename) {
    if (!confirm(`Are you sure you want to delete "${filename}"? This action cannot be undone.`)) {
        return;
    }
    
    try {
        showLoading('Deleting file...');
        
        await puter.fs.delete(filename);
        
        showToast('File deleted successfully!', 'success');
        loadUploadedFiles();
        
    } catch (error) {
        console.error('Error deleting file:', error);
        showToast('Error deleting file', 'error');
    } finally {
        hideLoading();
    }
}

// Utility Functions
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function getFileType(filename) {
    const ext = filename.split('.').pop().toLowerCase();
    const types = {
        'jpg': 'Image', 'jpeg': 'Image', 'png': 'Image', 'gif': 'Image', 'svg': 'Image',
        'pdf': 'PDF', 'doc': 'Document', 'docx': 'Document', 'txt': 'Text',
        'mp4': 'Video', 'avi': 'Video', 'mov': 'Video',
        'mp3': 'Audio', 'wav': 'Audio', 'flac': 'Audio',
        'zip': 'Archive', 'rar': 'Archive', '7z': 'Archive',
        'js': 'JavaScript', 'html': 'HTML', 'css': 'CSS', 'json': 'JSON'
    };
    return types[ext] || 'File';
}

// Modal Functions
function setupModal() {
    const modalCloses = document.querySelectorAll('.modal-close');
    modalCloses.forEach(close => {
        close.addEventListener('click', closeModal);
    });
    
    editModal.addEventListener('click', (e) => {
        if (e.target === editModal) closeModal();
    });
}

function closeModal() {
    editModal.classList.remove('active');
    currentEditingSite = null;
}

// Toast Functions
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    toast.innerHTML = `
        <div style="display: flex; align-items: center; gap: 0.5rem;">
            <i class="fas fa-${getToastIcon(type)}"></i>
            <span>${message}</span>
        </div>
    `;
    
    document.getElementById('toast-container').appendChild(toast);
    
    setTimeout(() => {
        toast.remove();
    }, 5000);
}

function getToastIcon(type) {
    const icons = {
        'success': 'check-circle',
        'error': 'exclamation-circle',
        'warning': 'exclamation-triangle',
        'info': 'info-circle'
    };
    return icons[type] || 'info-circle';
}

// Loading Functions
function showLoading(message = 'Loading...') {
    // Create loading overlay
    const overlay = document.createElement('div');
    overlay.id = 'loading-overlay';
    overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 2000;
        color: white;
        font-size: 1.2rem;
    `;
    overlay.innerHTML = `
        <div style="text-align: center;">
            <i class="fas fa-spinner fa-spin" style="font-size: 2rem; margin-bottom: 1rem;"></i>
            <div>${message}</div>
        </div>
    `;
    
    document.body.appendChild(overlay);
}

function hideLoading() {
    const overlay = document.getElementById('loading-overlay');
    if (overlay) {
        overlay.remove();
    }
}
